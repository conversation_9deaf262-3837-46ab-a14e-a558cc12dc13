using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using PanelApi.Attributes;

namespace PanelApi.Controllers;

/// <summary>
/// Ödeme sağlayıcısı yönetimi controller'ı
/// </summary>
[ApiController]
[Route("api/[controller]")]
[EnableCors("AllowFrontend")]
[Authorize]
public class PaymentProviderController : ControllerBase
{
    private readonly IPaymentProviderService _paymentProviderService;

    public PaymentProviderController(IPaymentProviderService paymentProviderService)
    {
        _paymentProviderService = paymentProviderService;
    }

    /// <summary>
    /// Tüm ödeme sağlayıcılarını listele
    /// </summary>
    [HttpGet]
    [RequirePermission("payment", "read")]
    public async Task<ActionResult<IEnumerable<PaymentProviderDto>>> GetAll()
    {
        try
        {
            var providers = await _paymentProviderService.GetAllAsync();
            return Ok(providers);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// ID ile ödeme sağlayıcısı getir
    /// </summary>
    [HttpGet("{id}")]
    [RequirePermission("payment", "read")]
    public async Task<ActionResult<PaymentProviderDto>> GetById(Guid id)
    {
        try
        {
            var provider = await _paymentProviderService.GetByIdAsync(id);
            if (provider == null)
                return NotFound(new { message = "Payment provider not found" });

            return Ok(provider);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Kısa kod ile ödeme sağlayıcısı getir
    /// </summary>
    [HttpGet("shortcode/{shortCode}")]
    [RequirePermission("payment", "read")]
    public async Task<ActionResult<PaymentProviderDto>> GetByShortCode(string shortCode)
    {
        try
        {
            var provider = await _paymentProviderService.GetByShortCodeAsync(shortCode);
            if (provider == null)
                return NotFound(new { message = "Payment provider not found" });

            return Ok(provider);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Statik ID ile ödeme sağlayıcısı getir
    /// </summary>
    [HttpGet("static/{staticId}")]
    [RequirePermission("payment", "read")]
    public async Task<ActionResult<PaymentProviderDto>> GetByStaticId(int staticId)
    {
        try
        {
            var provider = await _paymentProviderService.GetByStaticIdAsync(staticId);
            if (provider == null)
                return NotFound(new { message = "Payment provider not found" });

            return Ok(provider);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Aktif ve implementasyonu olan ödeme sağlayıcılarını listele
    /// </summary>
    [HttpGet("available")]
    [RequirePermission("payment", "read")]
    public async Task<ActionResult<IEnumerable<PaymentProviderDto>>> GetAvailable()
    {
        try
        {
            var providers = await _paymentProviderService.GetActiveImplementedProvidersAsync();
            return Ok(providers);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Ödeme sağlayıcısı ayarlarını getir
    /// </summary>
    [HttpGet("{id}/settings")]
    [RequirePermission("payment", "read")]
    public async Task<ActionResult<Dictionary<string, string>>> GetSettings(Guid id)
    {
        try
        {
            var settings = await _paymentProviderService.GetSettingsAsync(id);
            if (settings == null)
                return NotFound(new { message = "Payment provider not found" });

            return Ok(settings);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Ödeme sağlayıcısı ayarlarını güncelle
    /// </summary>
    [HttpPut("{id}/settings")]
    [RequirePermission("payment", "update")]
    public async Task<ActionResult> UpdateSettings(Guid id, [FromBody] UpdatePaymentProviderSettingsDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var result = await _paymentProviderService.UpdateProviderSettingsAsync(id, dto);
            if (!result)
                return NotFound(new { message = "Payment provider not found" });

            return Ok(new { message = "Settings updated successfully" });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Ödeme sağlayıcısı aktif/pasif durumunu değiştir
    /// </summary>
    [HttpPut("{id}/toggle")]
    [RequirePermission("payment", "update")]
    public async Task<ActionResult> ToggleActive(Guid id)
    {
        try
        {
            var newStatus = await _paymentProviderService.ToggleActiveAsync(id);
            return Ok(new { message = "Status updated successfully", isActive = newStatus });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Ödeme sağlayıcısı ayarlarını doğrula
    /// </summary>
    [HttpPost("{id}/validate")]
    [RequirePermission("payment", "read")]
    public async Task<ActionResult> ValidateSettings(Guid id)
    {
        try
        {
            var isValid = await _paymentProviderService.ValidateProviderSettingsAsync(id);
            return Ok(new { 
                message = isValid ? "Settings are valid" : "Settings validation failed", 
                isValid = isValid 
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Ödeme sağlayıcısı kullanılabilir mi kontrol et
    /// </summary>
    [HttpGet("{id}/available")]
    [RequirePermission("payment", "read")]
    public async Task<ActionResult> IsAvailable(Guid id)
    {
        try
        {
            var isAvailable = await _paymentProviderService.IsProviderAvailableAsync(id);
            return Ok(new { 
                message = isAvailable ? "Provider is available" : "Provider is not available", 
                isAvailable = isAvailable 
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }
}
