using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using PanelApi.Attributes;

namespace PanelApi.Controllers;

[ApiController]
[Route("api/[controller]")]
[EnableCors("AllowFrontend")]
[Authorize]
public class ProductController : ControllerBase
{
    private readonly IProductService _productService;
    private readonly IProductAttributeMappingService _attributeMappingService;
    private readonly IProductImageService _imageService;
    private readonly IProductFaqService _faqService;
    private readonly IProductSeoService _seoService;
    private readonly IProductReviewService _reviewService;

    public ProductController(
        IProductService productService,
        IProductAttributeMappingService attributeMappingService,
        IProductImageService imageService,
        IProductFaqService faqService,
        IProductSeoService seoService,
        IProductReviewService reviewService)
    {
        _productService = productService;
        _attributeMappingService = attributeMappingService;
        _imageService = imageService;
        _faqService = faqService;
        _seoService = seoService;
        _reviewService = reviewService;
    }

    [HttpGet]
    [RequirePermission("product", "read")]
    public async Task<ActionResult<List<ProductDto>>> GetList(
        [FromQuery] int? page,
        [FromQuery] int? pageSize)
    {
        var products = await _productService.GetListAsync(page, pageSize);
        return Ok(products);
    }

    [HttpGet("dropdown")]
    [RequirePermission("product", "read")]
    public async Task<ActionResult<List<object>>> GetProductsForDropdown()
    {
        var products = await _productService.GetListAsync(null, null);
        var dropdownItems = products.Select(p => new {
            id = p.Id,
            name = p.Name
        }).ToList();
        return Ok(dropdownItems);
    }

    [HttpGet("{id}")]
    [RequirePermission("product", "read")]
    public async Task<ActionResult<ProductDto>> GetById(Guid id)
    {
        var product = await _productService.GetByIdAsync(id);
        if (product == null)
        {
            return NotFound();
        }
        return Ok(product);
    }

    [HttpPost]
    [RequirePermission("product", "create")]
    public async Task<ActionResult> Create([FromBody] ProductCreateDto dto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            // 1. Ürünü oluştur
            var productId = await _productService.CreateAsync(dto);

            // 2. Attribute mappings varsa ekle
            if (dto.AttributeMappings != null && dto.AttributeMappings.Count > 0)
            {
                await _attributeMappingService.CreateBulkAsync(productId, dto.AttributeMappings);
            }

            // 3. Images varsa ekle
            if (dto.Images != null && dto.Images.Count > 0)
            {
                await _imageService.CreateBulkAsync(productId, dto.Images);
            }

            // 4. FAQs varsa ekle
            if (dto.Faqs != null && dto.Faqs.Count > 0)
            {
                await _faqService.CreateBulkAsync(productId, dto.Faqs);
            }

            // 5. SEO varsa ekle
            if (dto.Seo != null)
            {
                await _seoService.CreateOrUpdateAsync(productId, dto.Seo);
            }

            // 6. Variants varsa ekle
            if (dto.Variants != null && dto.Variants.Count > 0)
            {
                await _productService.CreateVariantsAsync(productId, dto.Variants);
            }

            return Ok(new
            {
                message = "Ürün başarıyla oluşturuldu.",
                productId = productId
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "Ürün oluşturulurken bir hata oluştu.", error = ex.Message });
        }
    }

    [HttpPut("{id}")]
    [RequirePermission("product", "update")]
    public async Task<ActionResult> Update(Guid id, [FromBody] ProductUpdateDto dto)
    {
        if (id != dto.Id)
        {
            return BadRequest("ID uyuşmazlığı");
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            // 1. Ürünü güncelle
            await _productService.UpdateAsync(dto);

            // 2. Attribute mappings güncelle
            if (dto.AttributeMappings != null)
            {
                await _attributeMappingService.UpdateBulkAsync(id, dto.AttributeMappings);
            }
            else
            {
                await _attributeMappingService.DeleteByProductIdAsync(id);
            }

            // 4. FAQs güncelle
            if (dto.Faqs != null)
            {
                await _faqService.UpdateBulkAsync(id, dto.Faqs);
            }

            // 5. SEO güncelle
            if (dto.Seo != null)
            {
                await _seoService.CreateOrUpdateAsync(id, dto.Seo);
            }

            // 6. Variants güncelle
            if (dto.Variants != null)
            {
                await _productService.UpdateVariantsAsync(id, dto.Variants);
            }

            return Ok(new { message = "Ürün başarıyla güncellendi." });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "Ürün güncellenirken bir hata oluştu.", error = ex.Message });
        }
    }

    [HttpDelete("{id}")]
    [RequirePermission("product", "delete")]
    public async Task<ActionResult> Delete(Guid id)
    {
        try
        {
            await _productService.DeleteAsync(id);
            return Ok(new { message = "Ürün başarıyla silindi." });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "Ürün silinirken bir hata oluştu.", error = ex.Message });
        }
    }

    // Ürün attribute mappings'lerini getir
    [HttpGet("{id}/attributes")]
    [RequirePermission("product", "read")]
    public async Task<ActionResult<List<ProductAttributeMappingDto>>> GetProductAttributes(Guid id)
    {
        var mappings = await _attributeMappingService.GetByProductIdAsync(id);
        return Ok(mappings);
    }

    // Ürün attribute mappings'lerini güncelle
    [HttpPut("{id}/attributes")]
    [RequirePermission("product", "update")]
    public async Task<ActionResult> UpdateProductAttributes(Guid id, [FromBody] List<ProductAttributeMappingCreateDto> mappings)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            await _attributeMappingService.UpdateBulkAsync(id, mappings);
            return Ok(new { message = "Ürün nitelikleri başarıyla güncellendi." });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "Ürün nitelikleri güncellenirken bir hata oluştu.", error = ex.Message });
        }
    }

    // Ürün resimlerini getir
    [HttpGet("{id}/images")]
    public async Task<ActionResult<List<ProductImageDto>>> GetProductImages(Guid id)
    {
        var images = await _imageService.GetByProductIdAsync(id);
        return Ok(images);
    }

    // Ürün ana resmini getir
    [HttpGet("{id}/images/main")]
    [RequirePermission("product", "read")]
    public async Task<ActionResult<ProductImageDto>> GetProductMainImage(Guid id)
    {
        var mainImage = await _imageService.GetMainImageByProductIdAsync(id);
        if (mainImage == null)
        {
            return NotFound(new { message = "Ana resim bulunamadı." });
        }
        return Ok(mainImage);
    }

    // Ürün resimlerini güncelle
    [HttpPut("{id}/images")]
    [RequirePermission("product", "update")]
    public async Task<ActionResult> UpdateProductImages(Guid id, [FromBody] List<ProductImageCreateDto> images)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            await _imageService.UpdateBulkAsync(id, images);
            return Ok(new { message = "Ürün resimleri başarıyla güncellendi." });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "Ürün resimleri güncellenirken bir hata oluştu.", error = ex.Message });
        }
    }

    // Ana resim belirle
    [HttpPut("{id}/images/{imageId}/set-main")]
    [RequirePermission("product", "update")]
    public async Task<ActionResult> SetMainImage(Guid id, Guid imageId)
    {
        try
        {
            var result = await _imageService.SetMainImageAsync(id, imageId);
            if (result)
            {
                return Ok(new { message = "Ana resim başarıyla belirlendi." });
            }
            return BadRequest(new { message = "Ana resim belirlenirken bir hata oluştu." });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "Ana resim belirlenirken bir hata oluştu.", error = ex.Message });
        }
    }

    // Resim sıralama güncelle
    [HttpPut("{id}/images/sort")]
    [RequirePermission("product", "update")]
    public async Task<ActionResult> UpdateImageSortOrder(Guid id, [FromBody] List<ProductImageSortDto> sortOrders)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            await _imageService.UpdateSortOrderAsync(id, sortOrders);
            return Ok(new { message = "Resim sıralaması başarıyla güncellendi." });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "Resim sıralaması güncellenirken bir hata oluştu.", error = ex.Message });
        }
    }

    // ===== FAQ ENDPOINTS =====

    // Ürün FAQ'larını getir
    [HttpGet("{id}/faqs")]
    [RequirePermission("product", "read")]
    public async Task<ActionResult<List<ProductFaqDto>>> GetProductFaqs(Guid id)
    {
        var faqs = await _faqService.GetByProductIdAsync(id);
        return Ok(faqs);
    }

    // Ürün FAQ'larını güncelle
    [HttpPut("{id}/faqs")]
    [RequirePermission("product", "update")]
    public async Task<ActionResult> UpdateProductFaqs(Guid id, [FromBody] List<ProductFaqCreateDto> faqs)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            await _faqService.UpdateBulkAsync(id, faqs);
            return Ok(new { message = "Ürün FAQ'ları başarıyla güncellendi." });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "Ürün FAQ'ları güncellenirken bir hata oluştu.", error = ex.Message });
        }
    }

    // FAQ sıralama güncelle
    [HttpPut("{id}/faqs/sort")]
    [RequirePermission("product", "update")]
    public async Task<ActionResult> UpdateFaqSortOrder(Guid id, [FromBody] List<ProductFaqSortDto> sortOrders)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            await _faqService.UpdateSortOrderAsync(id, sortOrders);
            return Ok(new { message = "FAQ sıralaması başarıyla güncellendi." });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "FAQ sıralaması güncellenirken bir hata oluştu.", error = ex.Message });
        }
    }

    // ===== SEO ENDPOINTS =====

    // Ürün SEO bilgilerini getir
    [HttpGet("{id}/seo")]
    [RequirePermission("product", "read")]
    public async Task<ActionResult<ProductSeoDto>> GetProductSeo(Guid id)
    {
        var seo = await _seoService.GetByProductIdAsync(id);
        if (seo == null)
        {
            return NotFound(new { message = "SEO bilgisi bulunamadı." });
        }
        return Ok(seo);
    }

    // Ürün SEO bilgilerini güncelle
    [HttpPut("{id}/seo")]
    [RequirePermission("product", "update")]
    public async Task<ActionResult> UpdateProductSeo(Guid id, [FromBody] ProductSeoDto seo)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            await _seoService.CreateOrUpdateAsync(id, seo);
            return Ok(new { message = "Ürün SEO bilgileri başarıyla güncellendi." });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "Ürün SEO bilgileri güncellenirken bir hata oluştu.", error = ex.Message });
        }
    }

    // Otomatik SEO oluştur
    [HttpPost("{id}/seo/auto-generate")]
    [RequirePermission("product", "update")]
    public async Task<ActionResult> GenerateAutoSeo(Guid id)
    {
        try
        {
            // Önce ürün bilgilerini al
            var product = await _productService.GetByIdAsync(id);
            if (product == null)
            {
                return NotFound(new { message = "Ürün bulunamadı." });
            }

            var result = await _seoService.GenerateAutoSeoAsync(id, product.Name, product.Description);
            if (result)
            {
                return Ok(new { message = "Otomatik SEO bilgileri başarıyla oluşturuldu." });
            }
            return BadRequest(new { message = "Otomatik SEO oluşturulurken bir hata oluştu." });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "Otomatik SEO oluşturulurken bir hata oluştu.", error = ex.Message });
        }
    }

    // ===== REVIEW ENDPOINTS =====

    // Ürün yorumlarını getir
    [HttpGet("{id}/reviews")]
    [RequirePermission("product", "read")]
    public async Task<ActionResult<ProductReviewListDto>> GetProductReviews(
        Guid id,
        [FromQuery] int? rating,
        [FromQuery] Guid? userId,
        [FromQuery] DateTime? startDate,
        [FromQuery] DateTime? endDate,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? sortBy = "CreatedAt",
        [FromQuery] string? sortDirection = "desc",
        [FromQuery] bool includeDeleted = false)
    {
        try
        {
            var filter = new ProductReviewFilterDto
            {
                Rating = rating,
                UserId = userId,
                StartDate = startDate,
                EndDate = endDate,
                PageNumber = pageNumber,
                PageSize = pageSize,
                SortBy = sortBy,
                SortDirection = sortDirection,
                IncludeDeleted = includeDeleted
            };

            var reviews = await _reviewService.GetByProductIdAsync(id, filter);
            return Ok(reviews);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Yorumlar getirilirken bir hata oluştu.", error = ex.Message });
        }
    }

    // Ürün yorum istatistiklerini getir
    [HttpGet("{id}/reviews/stats")]
    [RequirePermission("product", "read")]
    public async Task<ActionResult<ProductReviewStatsDto>> GetProductReviewStats(Guid id)
    {
        try
        {
            var stats = await _reviewService.GetStatsAsync(id);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Yorum istatistikleri getirilirken bir hata oluştu.", error = ex.Message });
        }
    }

    [HttpOptions]
    public IActionResult Options()
    {
        return Ok();
    }
}
