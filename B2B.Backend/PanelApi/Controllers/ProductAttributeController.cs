using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace PanelApi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ProductAttributeController : ControllerBase
{
    private readonly IProductAttributeService _productAttributeService;

    public ProductAttributeController(IProductAttributeService productAttributeService)
    {
        _productAttributeService = productAttributeService;
    }

    [HttpGet]
    public async Task<ActionResult<List<ProductAttributeDto>>> GetList(
        [FromQuery] int? page,
        [FromQuery] int? pageSize,
        [FromQuery] Guid? categoryId,
        [FromQuery] bool? isVariantAttribute)
    {
        var attributes = await _productAttributeService.GetListAsync(page, pageSize, categoryId, isVariantAttribute);
        return Ok(attributes);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<ProductAttributeDto>> GetById(Guid id)
    {
        var attribute = await _productAttributeService.GetByIdAsync(id);
        if (attribute == null)
        {
            return NotFound();
        }
        return Ok(attribute);
    }

    [HttpGet("category/{categoryId}")]
    public async Task<ActionResult<List<ProductAttributeDto>>> GetByCategory(Guid categoryId)
    {
        var attributes = await _productAttributeService.GetByCategoryIdAsync(categoryId);
        return Ok(attributes);
    }

    [HttpOptions]
    public IActionResult Options()
    {
        return Ok();
    }

    [HttpPost]
    public async Task<ActionResult> Create([FromBody] ProductAttributeCreateDto productAttribute)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }
        await _productAttributeService.CreateAsync(productAttribute);
        return Ok(new { message = "Nitelik başarıyla oluşturuldu." });
    }

    [HttpPut("{id}")]
    public async Task<ActionResult> Update(Guid id, [FromBody] ProductAttributeUpdateDto productAttribute)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        // Ensure the ID from the route matches the DTO
        if (productAttribute.Id != id)
        {
            return BadRequest("ID mismatch between route and body.");
        }

        await _productAttributeService.UpdateAsync(productAttribute);
        return Ok(new { message = "Nitelik başarıyla güncellendi." });
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult> Delete(Guid id, [FromQuery] bool force = false)
    {
        try
        {
            await _productAttributeService.DeleteAsync(id, force);
            return Ok(new { message = "Nitelik başarıyla silindi." });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { message = ex.Message });
        }
    }
}
