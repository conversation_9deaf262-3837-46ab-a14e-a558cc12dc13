using Application.Contracts.DTOs;

namespace Application.Contracts.Interfaces;

public interface IPaymentProviderService
{
    Task<IEnumerable<PaymentProviderDto>> GetAllAsync();
    Task<PaymentProviderDto?> GetByIdAsync(Guid id);
    Task<PaymentProviderDto?> GetByShortCodeAsync(string shortCode);
    Task<PaymentProviderDto?> GetByStaticIdAsync(int staticId);
    Task<IEnumerable<PaymentProviderDto>> GetActiveImplementedProvidersAsync();
    Task<Dictionary<string, string>?> GetSettingsAsync(Guid id);
    Task<bool> UpdateProviderSettingsAsync(Guid id, UpdatePaymentProviderSettingsDto dto);
    Task<bool> ToggleActiveAsync(Guid id);
    Task<bool> ValidateProviderSettingsAsync(Guid id);
    Task<bool> IsProviderAvailableAsync(Guid id);
}

