using Application.Contracts.DTOs;

namespace Application.Contracts.Interfaces;

public interface IProductAttributeMappingService
{
    Task<List<ProductAttributeMappingDto>> GetByProductIdAsync(Guid productId);
    Task<ProductAttributeMappingDto?> GetByIdAsync(Guid id);
    Task<bool> CreateAsync(ProductAttributeMappingCreateDto dto);
    Task<bool> UpdateAsync(ProductAttributeMappingUpdateDto dto);
    Task<bool> DeleteAsync(Guid id);
    Task<bool> CreateBulkAsync(Guid productId, List<ProductAttributeMappingCreateDto> mappings);
    Task<bool> UpdateBulkAsync(Guid productId, List<ProductAttributeMappingCreateDto> mappings);
    Task DeleteByProductIdAsync(Guid productId);
}
