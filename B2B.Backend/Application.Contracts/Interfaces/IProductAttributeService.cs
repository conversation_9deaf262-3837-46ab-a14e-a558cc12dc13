using Application.Contracts.DTOs;

namespace Application.Contracts.Interfaces;

public interface IProductAttributeService
{
    Task<List<ProductAttributeDto>> GetListAsync(int? page, int? pageSize, Guid? categoryId = null, bool? isVariantAttribute = null);
    Task<ProductAttributeDto?> GetByIdAsync(Guid id);
    Task<List<ProductAttributeDto>> GetByCategoryIdAsync(Guid categoryId);
    Task CreateAsync(ProductAttributeCreateDto dto);
    Task UpdateAsync(ProductAttributeUpdateDto dto);
    Task DeleteAsync(Guid id, bool force = false);
}