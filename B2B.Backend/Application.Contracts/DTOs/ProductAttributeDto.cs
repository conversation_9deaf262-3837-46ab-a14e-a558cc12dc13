using Core.Entities;

namespace Application.Contracts.DTOs;

public class ProductAttributeDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = null!;
    public string ShortName { get; set; } = null!;
    public bool IsVariantAttribute { get; set; }
    public bool IsListAttribute { get; set; }
    public bool IsActive { get; set; }
    public ICollection<ProductAttributeValueDto> Values { get; set; } = new List<ProductAttributeValueDto>();
    public int ValueCount => Values.Count;
    public ICollection<ProductCategoryDto> Categories { get; set; } = new List<ProductCategoryDto>();
    public int CategoryCount => Categories.Count;
}

public class ProductAttributeCreateDto
{
    public string Name { get; set; } = null!;
    public string ShortName { get; set; } = null!;
    public bool IsVariantAttribute { get; set; }
    public bool IsListAttribute { get; set; }
    public List<Guid> CategoryIds { get; set; } = new();
    public List<ProductAttributeValueDto> Values { get; set; } = new();
}

public class ProductAttributeUpdateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = null!;
    public string ShortName { get; set; } = null!;
    public bool IsVariantAttribute { get; set; }
    public bool IsListAttribute { get; set; }
    public List<Guid> CategoryIds { get; set; } = new();
    public List<ProductAttributeValueDto> Values { get; set; } = new();
}
