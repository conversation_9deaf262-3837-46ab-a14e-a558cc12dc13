namespace Application.Contracts.DTOs;

/// <summary>
/// Ödeme sağlayıcısı ayarlarını güncelleme DTO'su
/// </summary>
public class UpdatePaymentProviderSettingsDto
{
    /// <summary>
    /// API URL
    /// </summary>
    public string? ApiUrl { get; set; }

    /// <summary>
    /// API anahtarı
    /// </summary>
    public string? ApiKey { get; set; }

    /// <summary>
    /// Gizli anahtar
    /// </summary>
    public string? SecretKey { get; set; }

    /// <summary>
    /// Logo URL
    /// </summary>
    public string? LogoUrl { get; set; }

    /// <summary>
    /// Sıralama
    /// </summary>
    public int SortOrder { get; set; }

    /// <summary>
    /// Aktif mi?
    /// </summary>
    public bool IsActive { get; set; }
}
