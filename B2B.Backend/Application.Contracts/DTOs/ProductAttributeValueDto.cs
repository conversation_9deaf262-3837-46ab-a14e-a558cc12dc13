namespace Application.Contracts.DTOs;

public class ProductAttributeValueDto
{
    public Guid Id { get; set; }
    public Guid AttributeId { get; set; }
    public string Value { get; set; } = null!;
    public bool IsActive { get; set; }
    public ProductAttributeDto? Attribute { get; set; }
}

public class ProductAttributeValueCreateDto
{
    public Guid AttributeId { get; set; }
    public string Value { get; set; } = null!;
}

public class ProductAttributeValueUpdateDto
{
    public Guid Id { get; set; }
    public Guid AttributeId { get; set; }
    public string Value { get; set; } = null!;
}