using Core.Entities;

namespace Application.Contracts.Repositories;

public interface IPaymentProviderRepository
{
    Task<IEnumerable<PaymentProvider>> GetAllAsync();
    Task<PaymentProvider?> GetByIdAsync(Guid id);
    Task<PaymentProvider?> GetByShortCodeAsync(string shortCode);
    Task<PaymentProvider?> GetByStaticIdAsync(int staticId);
    Task<IEnumerable<PaymentProvider>> GetActiveImplementedProvidersAsync();
    void Update(PaymentProvider provider);
    Task SaveChangesAsync();
}

