using Application.Contracts.Repositories;
using Core.Entities;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repositories;

public class PaymentProviderRepository : IPaymentProviderRepository
{
    private readonly B2BDbContext _context;

    public PaymentProviderRepository(B2BDbContext context)
    {
        _context = context;
    }

    public async Task<IEnumerable<PaymentProvider>> GetAllAsync()
    {
        return await _context.Set<PaymentProvider>()
            .Where(x => !x.IsDeleted)
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .ToListAsync();
    }

    public async Task<PaymentProvider?> GetByIdAsync(Guid id)
    {
        return await _context.Set<PaymentProvider>().FirstOrDefaultAsync(x => x.Id == id && !x.IsDeleted);
    }

    public async Task<PaymentProvider?> GetByShortCodeAsync(string shortCode)
    {
        return await _context.Set<PaymentProvider>().FirstOrDefaultAsync(x => x.ShortCode == shortCode && !x.IsDeleted);
    }

    public async Task<PaymentProvider?> GetByStaticIdAsync(int staticId)
    {
        return await _context.Set<PaymentProvider>().FirstOrDefaultAsync(x => x.StaticId == staticId && !x.IsDeleted);
    }

    public async Task<IEnumerable<PaymentProvider>> GetActiveImplementedProvidersAsync()
    {
        return await _context.Set<PaymentProvider>()
            .Where(x => !x.IsDeleted && x.IsActive && x.IsImplemented)
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .ToListAsync();
    }

    public void Update(PaymentProvider provider)
    {
        _context.Set<PaymentProvider>().Update(provider);
    }

    public async Task SaveChangesAsync()
    {
        await _context.SaveChangesAsync();
    }
}

