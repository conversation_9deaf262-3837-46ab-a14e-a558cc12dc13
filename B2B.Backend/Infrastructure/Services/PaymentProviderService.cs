using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Application.Contracts.Repositories;

namespace Infrastructure.Services;

public class PaymentProviderService : IPaymentProviderService
{
    private readonly IPaymentProviderRepository _repo;

    public PaymentProviderService(IPaymentProviderRepository repo)
    {
        _repo = repo;
    }

    public async Task<IEnumerable<PaymentProviderDto>> GetAllAsync()
    {
        var entities = await _repo.GetAllAsync();
        return entities.Select(MapToDto);
    }

    public async Task<PaymentProviderDto?> GetByIdAsync(Guid id)
    {
        var entity = await _repo.GetByIdAsync(id);
        return entity == null ? null : MapToDto(entity);
    }

    public async Task<PaymentProviderDto?> GetByShortCodeAsync(string shortCode)
    {
        var entity = await _repo.GetByShortCodeAsync(shortCode);
        return entity == null ? null : MapToDto(entity);
    }

    public async Task<PaymentProviderDto?> GetByStaticIdAsync(int staticId)
    {
        var entity = await _repo.GetByStaticIdAsync(staticId);
        return entity == null ? null : MapToDto(entity);
    }

    public async Task<IEnumerable<PaymentProviderDto>> GetActiveImplementedProvidersAsync()
    {
        var entities = await _repo.GetActiveImplementedProvidersAsync();
        return entities.Select(MapToDto);
    }

    public async Task<Dictionary<string, string>?> GetSettingsAsync(Guid id)
    {
        var entity = await _repo.GetByIdAsync(id);
        if (entity == null) return null;

        var dict = new Dictionary<string, string>();
        if (!string.IsNullOrWhiteSpace(entity.ApiUrl)) dict["BaseUrl"] = entity.ApiUrl!;
        if (!string.IsNullOrWhiteSpace(entity.ApiKey)) dict["ApiKey"] = entity.ApiKey!;
        if (!string.IsNullOrWhiteSpace(entity.SecretKey)) dict["SecretKey"] = entity.SecretKey!;
        return dict;
    }

    public async Task<bool> UpdateProviderSettingsAsync(Guid id, UpdatePaymentProviderSettingsDto dto)
    {
        var entity = await _repo.GetByIdAsync(id);
        if (entity == null) return false;

        entity.ApiUrl = dto.ApiUrl;
        entity.ApiKey = dto.ApiKey;
        entity.SecretKey = dto.SecretKey;
        entity.LogoUrl = dto.LogoUrl;
        entity.SortOrder = dto.SortOrder;
        entity.IsActive = dto.IsActive;
        entity.UpdatedAt = DateTime.UtcNow;

        _repo.Update(entity);
        await _repo.SaveChangesAsync();
        return true;
    }

    public async Task<bool> ToggleActiveAsync(Guid id)
    {
        var entity = await _repo.GetByIdAsync(id);
        if (entity == null) return false;

        entity.IsActive = !entity.IsActive;
        entity.UpdatedAt = DateTime.UtcNow;

        _repo.Update(entity);
        await _repo.SaveChangesAsync();
        return entity.IsActive;
    }

    public async Task<bool> ValidateProviderSettingsAsync(Guid id)
    {
        var entity = await _repo.GetByIdAsync(id);
        if (entity == null) return false;

        // TODO: Implement payment provider validation
        return true;
    }

    public async Task<bool> IsProviderAvailableAsync(Guid id)
    {
        var entity = await _repo.GetByIdAsync(id);
        if (entity == null) return false;

        return entity.IsActive && entity.IsImplemented && !entity.IsDeleted;
    }

    private static PaymentProviderDto MapToDto(Core.Entities.PaymentProvider entity)
    {
        return new PaymentProviderDto
        {
            Id = entity.Id,
            Name = entity.Name,
            ShortCode = entity.ShortCode,
            Description = entity.Description,
            IsImplemented = entity.IsImplemented,
            IsActive = entity.IsActive,
            ApiUrl = entity.ApiUrl,
            ApiKey = entity.ApiKey,
            SecretKey = entity.SecretKey,
            LogoUrl = entity.LogoUrl,
            SortOrder = entity.SortOrder
        };
    }
}
