using System.Text.Json;
using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Interfaces;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shipping.Abstraction;

namespace Infrastructure.Services;

/// <summary>
/// Kargo firması servisi implementasyonu
/// </summary>
public class ShippingCarrierService : IShippingCarrierService
{
    private readonly IGenericRepository<ShippingCarrier> _carrierRepository;
    private readonly IShippingServiceFactory _shippingServiceFactory;
    private readonly ILogger<ShippingCarrierService> _logger;

    public ShippingCarrierService(
        IGenericRepository<ShippingCarrier> carrierRepository,
        IShippingServiceFactory shippingServiceFactory,
        ILogger<ShippingCarrierService> logger)
    {
        _carrierRepository = carrierRepository;
        _shippingServiceFactory = shippingServiceFactory;
        _logger = logger;
    }

    public async Task<IEnumerable<ShippingCarrierDto>> GetAllAsync()
    {
        try
        {
            var carriers = await _carrierRepository.Query()
                .Where(c => !c.IsDeleted)
                .OrderBy(c => c.SortOrder)
                .ThenBy(c => c.Name)
                .ToListAsync();

            return carriers.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all shipping carriers");
            throw;
        }
    }

    public async Task<ShippingCarrierDto?> GetByIdAsync(Guid id)
    {
        try
        {
            var carrier = await _carrierRepository.Query()
                .FirstOrDefaultAsync(c => c.Id == id && !c.IsDeleted);

            return carrier != null ? MapToDto(carrier) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting shipping carrier by ID: {Id}", id);
            throw;
        }
    }

    public async Task<ShippingCarrierDto?> GetByShortCodeAsync(string shortCode)
    {
        try
        {
            var carrier = await _carrierRepository.Query()
                .FirstOrDefaultAsync(c => c.ShortCode == shortCode && !c.IsDeleted);

            return carrier != null ? MapToDto(carrier) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting shipping carrier by short code: {ShortCode}", shortCode);
            throw;
        }
    }

    public async Task<ShippingCarrierDto?> GetByStaticIdAsync(int staticId)
    {
        try
        {
            var carrier = await _carrierRepository.Query()
                .FirstOrDefaultAsync(c => c.StaticId == staticId && !c.IsDeleted);

            return carrier != null ? MapToDto(carrier) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting shipping carrier by static ID: {StaticId}", staticId);
            throw;
        }
    }

    public async Task<IEnumerable<ShippingCarrierDto>> GetActiveImplementedCarriersAsync()
    {
        try
        {
            var carriers = await _carrierRepository.Query()
                .Where(c => !c.IsDeleted && c.IsActive && c.IsImplemented)
                .OrderBy(c => c.SortOrder)
                .ThenBy(c => c.Name)
                .ToListAsync();

            return carriers.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active implemented shipping carriers");
            throw;
        }
    }

    public async Task<Dictionary<string, string>?> GetCarrierSettingsAsync(Guid id)
    {
        try
        {
            var carrier = await _carrierRepository.GetByIdAsync(id);
            if (carrier == null || carrier.IsDeleted)
                return null;

            if (string.IsNullOrEmpty(carrier.Settings))
                return new Dictionary<string, string>();

            return JsonSerializer.Deserialize<Dictionary<string, string>>(carrier.Settings) 
                   ?? new Dictionary<string, string>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting carrier settings for ID: {Id}", id);
            throw;
        }
    }

    public async Task<bool> UpdateCarrierSettingsAsync(Guid id, UpdateShippingCarrierSettingsDto dto)
    {
        try
        {
            var carrier = await _carrierRepository.GetByIdAsync(id);
            if (carrier == null || carrier.IsDeleted)
            {
                _logger.LogWarning("Shipping carrier not found for settings update: {Id}", id);
                return false;
            }

            // Mevcut ayarları al
            var currentSettings = new Dictionary<string, string>();

            // Yeni ayarları birleştir
            foreach (var setting in dto.Settings)
            {
                currentSettings[setting.Key] = setting.Value;
            }

            // Ayarları JSON olarak kaydet
            carrier.Settings = JsonSerializer.Serialize(currentSettings);
            carrier.UpdatedAt = DateTime.UtcNow;

            _carrierRepository.Update(carrier);
            await _carrierRepository.SaveChangesAsync();

            _logger.LogInformation("Updated settings for shipping carrier: {Name} ({ShortCode})", 
                carrier.Name, carrier.ShortCode);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating carrier settings for ID: {Id}", id);
            throw;
        }
    }

    public async Task<bool> ToggleActiveAsync(Guid id)
    {
        try
        {
            var carrier = await _carrierRepository.GetByIdAsync(id);
            if (carrier == null || carrier.IsDeleted)
            {
                _logger.LogWarning("Shipping carrier not found for toggle: {Id}", id);
                return false;
            }

            carrier.IsActive = !carrier.IsActive;
            carrier.UpdatedAt = DateTime.UtcNow;

            _carrierRepository.Update(carrier);
            await _carrierRepository.SaveChangesAsync();

            _logger.LogInformation("Toggled active status for shipping carrier: {Name} ({ShortCode}) - New status: {IsActive}", 
                carrier.Name, carrier.ShortCode, carrier.IsActive);

            return carrier.IsActive;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling carrier active status for ID: {Id}", id);
            throw;
        }
    }

    public async Task<bool> ValidateCarrierSettingsAsync(Guid id)
    {
        try
        {
            var carrier = await _carrierRepository.GetByIdAsync(id);
            if (carrier == null || carrier.IsDeleted)
            {
                _logger.LogWarning("Shipping carrier not found for validation: {Id}", id);
                return false;
            }

            if (!carrier.IsImplemented)
            {
                _logger.LogWarning("Shipping carrier has no implementation: {Name} ({ShortCode})", 
                    carrier.Name, carrier.ShortCode);
                return false;
            }

            // Factory'den servisi al
            var shippingService = _shippingServiceFactory.GetService(carrier.Id);
            if (shippingService == null)
            {
                _logger.LogWarning("Shipping service not found in factory for carrier: {Name} ({ShortCode})", 
                    carrier.Name, carrier.ShortCode);
                return false;
            }

            // Ayarları al
            var settings = await GetCarrierSettingsAsync(id);
            if (settings == null)
            {
                _logger.LogWarning("No settings found for carrier: {Name} ({ShortCode})", 
                    carrier.Name, carrier.ShortCode);
                return false;
            }

            // Ayarları doğrula
            var isValid = await shippingService.ValidateSettingsAsync(settings);
            
            _logger.LogInformation("Settings validation result for carrier {Name} ({ShortCode}): {IsValid}", 
                carrier.Name, carrier.ShortCode, isValid);

            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating carrier settings for ID: {Id}", id);
            return false;
        }
    }

    public async Task<bool> IsCarrierAvailableAsync(Guid id)
    {
        try
        {
            var carrier = await _carrierRepository.GetByIdAsync(id);
            return carrier != null && !carrier.IsDeleted && carrier.IsActive && carrier.IsImplemented;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking carrier availability for ID: {Id}", id);
            return false;
        }
    }

    private static ShippingCarrierDto MapToDto(ShippingCarrier carrier)
    {
        var settings = string.IsNullOrEmpty(carrier.Settings) 
            ? new Dictionary<string, string>() 
            : JsonSerializer.Deserialize<Dictionary<string, string>>(carrier.Settings) 
              ?? new Dictionary<string, string>();

        return new ShippingCarrierDto
        {
            Id = carrier.Id,
            StaticId = carrier.StaticId,
            Name = carrier.Name,
            ShortCode = carrier.ShortCode,
            Description = carrier.Description,
            IsImplemented = carrier.IsImplemented,
            IsActive = carrier.IsActive,
            ApiUrl = carrier.ApiUrl,
            LogoUrl = carrier.LogoUrl,
            Settings = settings,
            SortOrder = carrier.SortOrder,
            CreatedAt = carrier.CreatedAt,
            UpdatedAt = carrier.UpdatedAt
        };
    }
}
