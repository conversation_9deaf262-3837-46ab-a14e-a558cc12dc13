using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Interfaces;

namespace Infrastructure.Services;

public class ProductAttributeValueService : IProductAttributeValueService
{
    private readonly IGenericRepository<ProductAttributeValue> _repository;

    public ProductAttributeValueService(IGenericRepository<ProductAttributeValue> repository)
    {
        _repository = repository;
    }
    public async Task<ProductAttributeValueDto?> GetByIdAsync(Guid id)
    {
        try
        {
            var productAttributeValue = await _repository.GetByIdAsync(id);
            return productAttributeValue == null ? null : new ProductAttributeValueDto
            {
                Id = productAttributeValue.Id,
                AttributeId = productAttributeValue.AttributeId,
                Value = productAttributeValue.Value,
                IsActive = productAttributeValue.IsActive
            };
        }
        catch
        {
            // Log the exception (ex) here if needed
            return null;
        }
    }

    public async Task<List<ProductAttributeValueDto>> GetListAsync(Guid attributeId,int? page, int? pageSize)
    {
        try
        {
            var productAttributeValues = await _repository.GetPagedAsync(page, pageSize);
            return productAttributeValues.Where(x=> x.AttributeId == attributeId).Select(x => new ProductAttributeValueDto
            {
                Id = x.Id,
                AttributeId = x.AttributeId,
                Value = x.Value,
                IsActive = x.IsActive
            }).ToList();
        }
        catch
        {
            // Log the exception (ex) here if needed
            return new List<ProductAttributeValueDto>();
        }
    }
    public async Task<bool> CreateAsync(ProductAttributeValueCreateDto productAttributeValueDto)
    {
        try
        {
            var productAttributeValue = new ProductAttributeValue
            {
                AttributeId = productAttributeValueDto.AttributeId,
                Value = productAttributeValueDto.Value
            };

            await _repository.AddAsync(productAttributeValue);
            await _repository.SaveChangesAsync();
            return true;
        }
        catch
        {
            // Log the exception (ex) here if needed
            return false;
        }
    }
    public async Task<bool> UpdateAsync(ProductAttributeValueUpdateDto productAttributeValueDto)
    {
        try
        {
            var productAttributeValue = await _repository.GetByIdAsync(productAttributeValueDto.Id);
            if (productAttributeValue == null)
            {
                return false;
            }

            productAttributeValue.AttributeId = productAttributeValueDto.AttributeId;
            productAttributeValue.Value = productAttributeValueDto.Value;

            _repository.Update(productAttributeValue);
            await _repository.SaveChangesAsync();
            return true;
        }
        catch
        {
            // Log the exception (ex) here if needed
            return false;
        }
    }
    public async Task<bool> DeleteAsync(Guid id)
    {
        try
        {
            var productAttributeValue = await _repository.GetByIdAsync(id);
            if (productAttributeValue == null || productAttributeValue.IsDeleted)
            {
                return false;
            }

            // Soft delete the attribute value
            productAttributeValue.IsActive = false;
            productAttributeValue.IsDeleted = true;
            productAttributeValue.UpdatedAt = DateTime.UtcNow;

            _repository.Update(productAttributeValue);
            await _repository.SaveChangesAsync();
            return true;
        }
        catch
        {
            // Log the exception (ex) here if needed
            return false;
        }
    }

    

   
}