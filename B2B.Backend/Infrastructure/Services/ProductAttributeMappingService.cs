using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Services;

public class ProductAttributeMappingService : IProductAttributeMappingService
{
    private readonly IGenericRepository<ProductAttributeMapping> _repository;

    public ProductAttributeMappingService(IGenericRepository<ProductAttributeMapping> repository)
    {
        _repository = repository;
    }

    public async Task<List<ProductAttributeMappingDto>> GetByProductIdAsync(Guid productId)
    {
        try
        {
            var mappings = await _repository.FindAsync(m => m.ProductId == productId);
            var productMappings = mappings
                .Select(m => new ProductAttributeMappingDto
                {
                    Id = m.Id,
                    ProductId = m.ProductId,
                    AttributeId = m.AttributeId,
                    AttributeValueId = m.AttributeValueId,
                    Attribute = m.Attribute != null ? new ProductAttributeDto
                    {
                        Id = m.Attribute.Id,
                        Name = m.Attribute.Name,
                        ShortName = m.Attribute.ShortName,
                        IsVariantAttribute = m.Attribute.IsVariantAttribute,
                        IsListAttribute = m.Attribute.IsListAttribute
                    } : null,
                    AttributeValue = m.AttributeValue != null ? new ProductAttributeValueDto
                    {
                        Id = m.AttributeValue.Id,
                        AttributeId = m.AttributeValue.AttributeId,
                        Value = m.AttributeValue.Value
                    } : null
                })
                .ToList();

            return productMappings;
        }
        catch
        {
            return [];
        }
    }

    public async Task<ProductAttributeMappingDto?> GetByIdAsync(Guid id)
    {
        try
        {
            var mapping = await _repository.GetByIdAsync(id);
            if (mapping == null) return null;

            return new ProductAttributeMappingDto
            {
                Id = mapping.Id,
                ProductId = mapping.ProductId,
                AttributeId = mapping.AttributeId,
                AttributeValueId = mapping.AttributeValueId,
                Attribute = mapping.Attribute != null ? new ProductAttributeDto
                {
                    Id = mapping.Attribute.Id,
                    Name = mapping.Attribute.Name,
                    ShortName = mapping.Attribute.ShortName,
                    IsVariantAttribute = mapping.Attribute.IsVariantAttribute,
                    IsListAttribute = mapping.Attribute.IsListAttribute
                } : null,
                AttributeValue = mapping.AttributeValue != null ? new ProductAttributeValueDto
                {
                    Id = mapping.AttributeValue.Id,
                    AttributeId = mapping.AttributeValue.AttributeId,
                    Value = mapping.AttributeValue.Value
                } : null
            };
        }
        catch
        {
            return null;
        }
    }

    public async Task<bool> CreateAsync(ProductAttributeMappingCreateDto dto)
    {
        try
        {
            if (!dto.ProductId.HasValue)
                return false; // ProductId zorunlu

            var mapping = new ProductAttributeMapping
            {
                ProductId = dto.ProductId.Value,
                AttributeId = dto.AttributeId,
                AttributeValueId = dto.AttributeValueId
            };

            await _repository.AddAsync(mapping);
            await _repository.SaveChangesAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<bool> UpdateAsync(ProductAttributeMappingUpdateDto dto)
    {
        try
        {
            var mapping = await _repository.GetByIdAsync(dto.Id);
            if (mapping == null) return false;

            mapping.ProductId = dto.ProductId;
            mapping.AttributeId = dto.AttributeId;
            mapping.AttributeValueId = dto.AttributeValueId;

            _repository.Update(mapping);
            await _repository.SaveChangesAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<bool> DeleteAsync(Guid id)
    {
        try
        {
            var mapping = await _repository.GetByIdAsync(id);
            if (mapping == null) return false;

            _repository.Delete(mapping);
            await _repository.SaveChangesAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<bool> CreateBulkAsync(Guid productId, List<ProductAttributeMappingCreateDto> mappings)
    {
        try
        {
            var entities = mappings.Select(dto => new ProductAttributeMapping
            {
                ProductId = productId,
                AttributeId = dto.AttributeId,
                AttributeValueId = dto.AttributeValueId
            }).ToList();

            await _repository.AddRangeAsync(entities);
            await _repository.SaveChangesAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<bool> UpdateBulkAsync(Guid productId, List<ProductAttributeMappingCreateDto> mappings)
    {
        try
        {
            // Önce mevcut mapping'leri sil
            var productMappings = await _repository.FindAsync(m => m.ProductId == productId);

            if (productMappings.Any())
            {
                _repository.DeleteRange(productMappings);
            }

            // Yeni mapping'leri ekle
            var newMappings = mappings.Select(dto => new ProductAttributeMapping
            {
                ProductId = productId,
                AttributeId = dto.AttributeId,
                AttributeValueId = dto.AttributeValueId
            }).ToList();

            if (newMappings.Count > 0)
            {
                await _repository.AddRangeAsync(newMappings);
            }

            await _repository.SaveChangesAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }
    public async Task DeleteByProductIdAsync(Guid productId)
    {
        try
        {
            var mappings = await _repository.FindAsync(m => m.ProductId == productId);
            if (mappings.Any())
            {
                _repository.DeleteRange(mappings);
                await _repository.SaveChangesAsync();
            }
        }
        catch
        {
            // Log error
        }
    }
}
