import Footer1 from "@/components/footers/Footer1";
import Header1 from "@/components/headers/Header1";
import Topbar2 from "@/components/headers/Topbar2";
import Breadcumb from "@/components/productDetails/Breadcumb";
import Description1 from "@/components/productDetails/Description1";

import Details4 from "@/components/productDetails/Details4";
import RecentlyViewedProducts from "@/components/productDetails/RecentlyViewedProducts";
import RecommendedProducts from "@/components/productDetails/RecommendedProducts";
import { getProductBySlugSSR } from "@/services/product";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import React from "react";
import { getCompanyInfo } from "@/lib/api/server";

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';
export const revalidate = 0; // Disable static generation

// Generate static params for popular products (optional optimization)
export async function generateStaticParams() {
  try {
    // This could fetch popular product IDs for pre-generation
    // For now, return empty array to allow all dynamic generation
    return [];
  } catch (error) {
    console.error('Error generating static params:', error);
    return [];
  }
}

// Optimized metadata generation with caching
export async function generateMetadata({ params }) {
  try {
    const { slug } = await params;

    // Get company info with caching
    const { getCompanyInfo } = await import('@/lib/api/server');
    const companyResponse = await getCompanyInfo();
    const companyInfo = companyResponse?.success ? companyResponse.data : null;
    const companyName = companyInfo?.metaTitle || companyInfo?.companyName || "B2C Mağaza";

    // Fetch product data for metadata generation
    const productData = await getProductBySlugSSR(slug);

    if (!productData?.success || !productData?.data) {
      return {
        title: `Ürün Bulunamadı | ${companyName}`,
        description: "Aradığınız ürün bulunamadı.",
        robots: "noindex, nofollow",
      };
    }

    const product = productData.data;

    // Try to get product SEO data using server API
    let productSeo = null;
    try {
      const { serverApi } = await import('@/lib/api/server');
      const seoResponse = await serverApi.get(`/productseo/product/${product.product.id}`);
      productSeo = seoResponse?.data || null;
    } catch (error) {
      console.error('Error fetching product SEO:', error);
    }

    // Generate comprehensive metadata using SEO data if available
    let title, description, keywords;

    if (productSeo?.metaTitle) {
      title = `${productSeo.metaTitle} | ${companyName}`;
    } else {
      title = `${product.metadata.title || product.product.name} | ${companyName}`;
    }

    if (productSeo?.metaDescription) {
      description = productSeo.metaDescription;
    } else {
      description = product.metadata.description || `${product.metadata.title || product.metadata.name} ürün detayları ve özellikleri`;
    }

    if (productSeo?.metaKeywords) {
      keywords = productSeo.metaKeywords;
    } else {
      keywords = product.metadata.keywords || `${product.metadata.title}, ${product.metadata.categoryName}`;
    }

    return {
      title,
      description,
      keywords,
      alternates: {
        canonical: productSeo?.canonicalUrl || `/urun-detay/${slug}`,
      },
      openGraph: {
        title: productSeo?.ogTitle || product.metadata.title || product.metadata.name,
        description: productSeo?.ogDescription || product.metadata.description,
        images: productSeo?.ogImage ? [productSeo.ogImage] : (product.metadata.imgSrc ? [product.metadata.imgSrc] : []),
        type: 'article',
        siteName: companyName,
      },
      twitter: {
        card: 'summary_large_image',
        title: productSeo?.ogTitle || product.metadata.title || product.metadata.name,
        description: productSeo?.ogDescription || product.metadata.description,
        images: productSeo?.ogImage ? [productSeo.ogImage] : (product.metadata.imgSrc ? [product.metadata.imgSrc] : []),
      },
      robots: productSeo?.noIndex ? "noindex" : "index, follow",
      ...(productSeo?.structuredData && {
        other: {
          'application/ld+json': productSeo.structuredData
        }
      })
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: "Ürün Detayı | B2C Mağaza",
      description: "Ürün detay sayfası",
    };
  }
}

// Loading skeleton component
function ProductDetailSkeleton() {
  return (
    <section className="flat-single-product">
      <div className="tf-main-product">
        <div className="container">
          <div className="row">
            {/* Product images skeleton */}
            <div className="col-md-6">
              <div className="animate-pulse">
                <div className="h-96 bg-gray-200 rounded-lg mb-4"></div>
                <div className="flex space-x-2">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="h-16 w-16 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </div>
            </div>

            {/* Product info skeleton */}
            <div className="col-md-6">
              <div className="animate-pulse space-y-6">
                <div className="space-y-2">
                  <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>

                <div className="space-y-2">
                  <div className="h-6 bg-gray-200 rounded w-1/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                </div>

                {/* Volume options skeleton */}
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-16"></div>
                  <div className="flex space-x-2">
                    <div className="h-10 w-20 bg-gray-200 rounded"></div>
                    <div className="h-10 w-20 bg-gray-200 rounded"></div>
                    <div className="h-10 w-20 bg-gray-200 rounded"></div>
                  </div>
                </div>

                {/* Add to cart skeleton */}
                <div className="space-y-4">
                  <div className="flex space-x-4">
                    <div className="h-12 w-24 bg-gray-200 rounded"></div>
                    <div className="h-12 flex-1 bg-gray-200 rounded"></div>
                  </div>
                  <div className="h-12 w-full bg-gray-200 rounded"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Recommended products skeleton */}
          <div className="mt-16 space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="space-y-2">
                  <div className="h-48 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// Error handling component - now as a separate client component
function ProductError() {
  return (
    <>
      <Topbar2 parentClass="tf-topbar bg-dark-5 topbar-bg" />
      <Header1 />
      <section className="flat-single-product">
        <div className="tf-main-product">
          <div className="container">
            <div className="row justify-content-center">
              <div className="col-md-8 text-center">
                <div className="alert alert-danger">
                  <h4>Ürün Yüklenemedi</h4>
                  <p>Ürün bilgileri yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.</p>
                  <a href="/urunler" className="tf-btn btn-primary">
                    Ürünlere Dön
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <Footer1 paddingBottom />
    </>
  );
}

export default async function ProductDetailPage({ params }) {
  try {
    const { slug } = await params;

    // Performance optimization: Start data fetching immediately
    const startTime = Date.now();

    // Server-side rendering ile ürün detayını getir
    const productData = await getProductBySlugSSR(slug);
    // Log performance metrics in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`Product data fetched in ${Date.now() - startTime}ms for slug: ${slug}`);
    }

    if (!productData?.success || !productData?.data) {
      notFound();
    }

    const product = productData.data.product;

    return (
      <>
        <Breadcumb product={product} />
        <Details4 product={product} />
        <Description1 product={product} />

        {/* Lazy load recommended products for better performance */}
        <Suspense fallback={
          <div className="space-y-6 animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="space-y-2">
                  <div className="h-48 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        }>
          {/* <RecommendedProducts product={product} /> */}
        </Suspense>

        {/* Lazy load related products */}
        <Suspense fallback={
          <div className="space-y-6 animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="space-y-2">
                  <div className="h-48 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        }>
          {/* <RecentlyViewedProducts /> */}
        </Suspense>

      </>
    );
  } catch (error) {
    console.error('Error in ProductDetailPage:', error);

    // Return error page without onClick handler
    return <ProductError />;
  }
}
