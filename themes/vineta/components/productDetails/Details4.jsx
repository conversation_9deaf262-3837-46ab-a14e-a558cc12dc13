"use client";
import React, { useState } from "react";

import BoughtTogether from "./BoughtTogether";
import SizePicker from "./SizeSelect";
import VolumeSelector from "./VolumeSelector";
import DiscountVolume from "./DiscountVolume";
import OutofStockForm from "./OutofStockForm";
import ColorPicker from "./ColorPicker";
import ShippingInfo from "./ShippingInfo";
import { useContextElement } from "@/context/Context";
import QuantitySelect from "../common/QuantitySelect";
import StickyProducts from "./StickyProducts";
import ProductHeading from "./ProductHeading";
import Slider3 from "./sliders/Slider3";
export default function Details4({ product, isLoading = false, error = null }) {
  const [quantity, setQuantity] = useState(1);
  const [activeColor, setActiveColor] = useState("Black");
  const [selectedVolume, setSelectedVolume] = useState(null);
  const [selectedCampaign, setSelectedCampaign] = useState(null);

  // Error state
  if (error) {
    return (
      <section className="flat-single-product">
        <div className="tf-main-product">
          <div className="container">
            <div className="row justify-content-center">
              <div className="col-md-8 text-center">
                <div className="alert alert-danger">
                  <h4>Ürün Yüklenemedi</h4>
                  <p>{error.message || "Ürün bilgileri yüklenirken bir hata oluştu."}</p>
                  <button
                    className="tf-btn btn-primary"
                    onClick={() => window.location.reload()}
                  >
                    Tekrar Dene
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  // Loading state
  if (isLoading || !product) {
    return (
      <section className="flat-single-product">
        <div className="tf-main-product">
          <div className="container">
            <div className="row">
              <div className="col-md-6">
                <div className="animate-pulse">
                  <div className="h-96 bg-gray-200 rounded-lg mb-4"></div>
                  <div className="flex space-x-2">
                    {[...Array(4)].map((_, i) => (
                      <div key={i} className="h-16 w-16 bg-gray-200 rounded"></div>
                    ))}
                  </div>
                </div>
              </div>
              <div className="col-md-6">
                <div className="animate-pulse space-y-6">
                  <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/4"></div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                    <div className="flex space-x-2">
                      {[...Array(3)].map((_, i) => (
                        <div key={i} className="h-10 w-10 bg-gray-200 rounded"></div>
                      ))}
                    </div>
                  </div>
                  <div className="h-12 bg-gray-200 rounded"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }
  const {
    addProductToCart,
    isAddedToCartProducts,
    addToWishlist,
    isAddedtoWishlist,
    addToCompareItem,
    isAddedtoCompareItem,
    cartProducts,
    updateQuantity,
  } = useContextElement();

  // Buy X Get Y Free kampanyası gösterim koşulları
  const hasBuyXGetYCampaigns = product?.campaigns &&
    product.campaigns.length > 0 &&
    product.campaigns.some(campaign => campaign.isBuyXGetYFree);

  // Kampanya seçimi handler'ı
  const handleCampaignSelect = (campaign) => {
    setSelectedCampaign(campaign);
    // Kampanya seçildiğinde quantity'yi otomatik güncelle
    if (campaign && campaign.totalQuantity) {
      setQuantity(campaign.totalQuantity);
    }
  };

  // Seçili volume'a göre fiyat hesaplama
  const getCurrentPrice = () => {
    if (selectedVolume && selectedVolume.price) {
      return selectedVolume.price;
    }
    return product?.price || 0;
  };
  return (
    <section className="flat-single-product">
      <div className="tf-main-product section-image-zoom">
        <div className="container">
          <div className="row">
            {/* Product Images */}
            <div className="col-md-6">
              <div className="tf-product-media-wrap sticky-top">
                <div className="thumbs-slider thumbs-bottom">
                  <Slider3
                    activeColor={activeColor}
                    firstItem={product?.imgSrc}
                    setActiveColor={setActiveColor}
                    slideItems={product?.images || []}
                  />
                </div>
              </div>
            </div>
            {/* /Product Images */}
            {/* Product Info */}
            <div className="col-md-6">
              <div className="tf-zoom-main" />
              <div className="tf-product-info-wrap position-relative">
                <div className="tf-product-info-list other-image-zoom">
                  <ProductHeading product={product} showProgress={true} />
                  <div className="tf-product-variant">
                    {product?.colors && product.colors.length > 0 && (
                      <ColorPicker
                        colors={product.colors}
                        activeColor={activeColor}
                        setActiveColor={setActiveColor}
                      />
                    )}
                    {product?.volumes && product.volumes.length > 0 && (
                      <VolumeSelector
                        volumes={product.volumes}
                        selectedVolume={selectedVolume}
                        onVolumeChange={setSelectedVolume}
                      />
                    )}
                    {product?.sizes && product.sizes.length > 0 && (
                      <SizePicker sizes={product.sizes} />
                    )}
                  </div>

                  {/* Buy X Get Y Free Kampanyası */}
                  {hasBuyXGetYCampaigns && product?.inStock && (
                    <DiscountVolume
                      campaigns={product.campaigns}
                      productPrice={getCurrentPrice()}
                      onCampaignSelect={handleCampaignSelect}
                      disabled={!product?.inStock}
                    />
                  )}

                  {/* Stokta Yok Formu */}
                  {!product?.inStock && (
                    <OutofStockForm />
                  )}

                  {/* Normal Sepete Ekleme - Sadece stokta varsa göster */}
                  {product?.inStock && (
                    <div className="tf-product-total-quantity">
                      <div className="group-btn">
                        <QuantitySelect
                          quantity={product?.id && isAddedToCartProducts(product.id) ? cartProducts.filter((elm) => elm.id == product.id)[0]?.quantity || quantity : quantity}
                          setQuantity={(qty) => {
                            if (product?.id && isAddedToCartProducts(product.id)) {
                              updateQuantity(product.id, qty);
                            } else {
                              setQuantity(qty);
                            }
                          }}
                        />
                        <a
                          href="#shoppingCart"
                          data-bs-toggle="offcanvas"
                          onClick={() => {
                            if (product?.id) {
                              // Kampanya seçilmişse kampanya quantity'sini kullan
                              const finalQuantity = selectedCampaign?.totalQuantity || quantity;
                              addProductToCart(product, finalQuantity);
                            }
                          }}
                          className={`tf-btn hover-primary btn-add-to-cart ${!product?.id || !product?.inStock ? 'disabled' : ''}`}
                          disabled={!product?.id || !product?.inStock}
                        >
                          {!product?.inStock ? "Stokta Yok" :
                            (product?.id && isAddedToCartProducts(product.id) ? "Sepete Eklendi" :
                              selectedCampaign ? `${selectedCampaign.name} - Sepete Ekle` : "Sepete Ekle")}
                        </a>
                      </div>
                      {/* <a href="#" className="tf-btn btn-primary w-100 animate-btn"> Hemen Satın Al </a> */}
                    </div>
                  )}

                  <div className="tf-product-extra-link">
                    <a
                      onClick={() => product?.id && addToWishlist(product)}
                      className={`product-extra-icon link btn-add-wishlist ${product?.id && isAddedtoWishlist(product.id) ? "added-wishlist" : ""} ${!product?.id ? 'disabled' : ''}`}
                      disabled={!product?.id}
                    >
                      <i className="icon add icon-heart" />
                      <span className="add">Favorilere Ekle</span>
                      <i className="icon added icon-trash" />
                      <span className="added">Favorilerden Çıkar</span>
                    </a>
                    {/*                     <a
                      href="#compare"
                      data-bs-toggle="modal"
                      onClick={() => addToCompareItem(product.id)}
                      className="product-extra-icon link"
                    >
                      <i className="icon icon-compare2" />
                      {isAddedtoCompareItem(product.id)
                        ? "Already compared"
                        : "Add to Compare"}
                    </a> 
                    */}
                    {/*             <a
                      href="#askQuestion"
                      data-bs-toggle="modal"
                      className="product-extra-icon link"
                    >
                      <i className="icon icon-ask" />
                      Ask a question
                    </a> */}
                    <a href="#shareSocial" data-bs-toggle="modal" className="product-extra-icon link"  >
                      <i className="icon icon-share" />
                      Paylaş
                    </a>
                  </div>
                  <ul className="tf-product-cate-sku text-md">
                    {product?.sku && (
                      <li className="item-cate-sku">
                        <span className="label">SKU:</span>
                        <span className="value">{product.sku}</span>
                      </li>
                    )}
                    {product?.categoryName && (
                      <li className="item-cate-sku">
                        <span className="label">Kategoriler:</span>
                        <span className="value">{product.categoryName}</span>
                      </li>
                    )}
                    {product?.brand && (
                      <li className="item-cate-sku">
                        <span className="label">Marka:</span>
                        <span className="value">{product.brand}</span>
                      </li>
                    )}
                  </ul>

                  <ShippingInfo product={product} />
                </div>
                {/* <div className="tf-product-fbt"> */}
                {/*   <div className="title text-xl fw-medium"> */}
                {/*     Frequently Bought Together */}
                {/*   </div> */}
                {/*   <BoughtTogether /> */}
                {/* </div> */}
              </div>
            </div>
            {/* /Product Info */}
          </div>
        </div>
      </div>
      {/* <StickyProducts /> */}
    </section>
  );
}
