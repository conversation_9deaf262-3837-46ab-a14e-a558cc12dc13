import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { hasServerPermission } from "@/lib/auth/server-permissions";
import { PermissionProvider } from "@/components/auth/permission-provider";
import ShippingCarriersListServer from "./components/ShippingCarriersListServer";
import PageHeaderServer from "../../components/PageHeaderServer";

export default async function ShippingSettingsPage() {
  // Server-side permission check - redirect if no access
  const canRead = await hasServerPermission("shipping", "read");
  if (!canRead) {
    redirect("/admin/dashboard");
  }

  const t = await getTranslations("shipping");

  return (
    <div className="space-y-6">
      <PermissionProvider resource="shipping">
        {({ read, create, update, delete: canDelete }) => (
          <>
            <PageHeaderServer
              title={t("title")}
              description={t("list")}
              actions={[]}
            />
            <ShippingCarriersListServer
              canRead={read}
              canCreate={create}
              canUpdate={update}
              canDelete={canDelete}
            />
          </>
        )}
      </PermissionProvider>
    </div>
  );
}
