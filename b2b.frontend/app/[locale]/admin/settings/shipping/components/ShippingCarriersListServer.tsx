import { Suspense } from 'react';
import ShippingCarriersListClient from './ShippingCarriersListClient';
import { Skeleton } from '@/components/ui/skeleton';

interface ShippingCarriersListServerProps {
  canRead: boolean;
  canCreate: boolean;
  canUpdate: boolean;
  canDelete: boolean;
}

export default function ShippingCarriersListServer({
  canRead,
  canCreate,
  canUpdate,
  canDelete
}: ShippingCarriersListServerProps) {
  if (!canRead) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">
          Bu sayfayı görüntüleme yetkiniz bulunmuyor.
        </p>
      </div>
    );
  }

  return (
    <Suspense fallback={<ShippingCarriersListSkeleton />}>
      <ShippingCarriersListClient
        canCreate={canCreate}
        canUpdate={canUpdate}
        canDelete={canDelete}
      />
    </Suspense>
  );
}

function ShippingCarriersListSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-10 w-32" />
      </div>
      <div className="space-y-2">
        {Array.from({ length: 5 }).map((_, i) => (
          <Skeleton key={i} className="h-16 w-full" />
        ))}
      </div>
    </div>
  );
}
