import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { hasServerPermission } from "@/lib/auth/server-permissions";
import { PermissionProvider } from "@/components/auth/permission-provider";
import PaymentProvidersListServer from "./components/PaymentProvidersListServer";
import PageHeaderServer from "../../components/PageHeaderServer";

export default async function PaymentProvidersPage() {
  // Server-side permission check - redirect if no access
  const canRead = await hasServerPermission("payment", "read");
  if (!canRead) {
    redirect("/admin/dashboard");
  }

  const t = await getTranslations("payment.providers");

  return (
    <div className="space-y-6">
      <PermissionProvider resource="payment">
        {({ read, create, update, delete: canDelete }) => (
          <>
            <PageHeaderServer
              title={t("title")}
              description={t("list")}
              actions={[]}
            />
            <PaymentProvidersListServer
              canRead={read}
              canCreate={create}
              canUpdate={update}
              canDelete={canDelete}
            />
          </>
        )}
      </PermissionProvider>
    </div>
  );
}
