import { Suspense } from 'react';
import PaymentProvidersListClient from './PaymentProvidersListClient';
import { Skeleton } from '@/components/ui/skeleton';

interface PaymentProvidersListServerProps {
  canRead: boolean;
  canCreate: boolean;
  canUpdate: boolean;
  canDelete: boolean;
}

export default function PaymentProvidersListServer({
  canRead,
  canCreate,
  canUpdate,
  canDelete
}: PaymentProvidersListServerProps) {
  if (!canRead) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">
          Bu sayfay<PERSON> görüntüleme yetkiniz bulunmuyor.
        </p>
      </div>
    );
  }

  return (
    <Suspense fallback={<PaymentProvidersListSkeleton />}>
      <PaymentProvidersListClient
        canCreate={canCreate}
        canUpdate={canUpdate}
        canDelete={canDelete}
      />
    </Suspense>
  );
}

function PaymentProvidersListSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-10 w-32" />
      </div>
      <div className="space-y-2">
        {Array.from({ length: 5 }).map((_, i) => (
          <Skeleton key={i} className="h-16 w-full" />
        ))}
      </div>
    </div>
  );
}
