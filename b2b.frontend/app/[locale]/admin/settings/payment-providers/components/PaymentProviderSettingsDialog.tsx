'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Save, X, Plus, Trash2 } from 'lucide-react';
import {
  usePaymentProviderSettings,
  useUpdatePaymentProviderSettings,
} from '@/hooks/usePaymentProviders';
import type { PaymentProviderDto, UpdatePaymentProviderSettingsDto } from '@/types/payment-provider';

interface PaymentProviderSettingsDialogProps {
  provider: PaymentProviderDto;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  canUpdate: boolean;
}

interface SettingsKeyValue {
  key: string;
  value: string;
  isNew?: boolean;
}

export default function PaymentProviderSettingsDialog({
  provider,
  open,
  onOpenChange,
  canUpdate
}: PaymentProviderSettingsDialogProps) {
  const t = useTranslations('payment.providers');
  const commonT = useTranslations('common');

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [settingsData, setSettingsData] = useState<SettingsKeyValue[]>([]);
  const [hasChanges, setHasChanges] = useState(false);

  const { data: settings, isLoading: isLoadingSettings } = usePaymentProviderSettings(
    provider?.id || ''
  );

  const updateSettingsMutation = useUpdatePaymentProviderSettings();

  // Load settings when dialog opens
  useEffect(() => {
    if (open && settings) {
      // Convert settings object to key-value array
      const settingsArray: SettingsKeyValue[] = Object.entries(settings).map(([key, value]) => ({
        key,
        value: value || '',
        isNew: false
      }));

      setSettingsData(settingsArray);
      setHasChanges(false);
    }
  }, [open, settings]);

  // Reset when dialog closes
  useEffect(() => {
    if (!open) {
      setSettingsData([]);
      setHasChanges(false);
    }
  }, [open]);

  const handleSettingChange = (index: number, field: 'key' | 'value', newValue: string) => {
    const updatedSettings = [...settingsData];
    updatedSettings[index][field] = newValue;
    setSettingsData(updatedSettings);
    setHasChanges(true);
  };

  const handleAddSetting = () => {
    const newSetting: SettingsKeyValue = {
      key: '',
      value: '',
      isNew: true
    };
    setSettingsData([...settingsData, newSetting]);
    setHasChanges(true);
  };

  const handleRemoveSetting = (index: number) => {
    const updatedSettings = settingsData.filter((_, i) => i !== index);
    setSettingsData(updatedSettings);
    setHasChanges(true);
  };

  const handleSubmit = async () => {
    if (!canUpdate || !hasChanges) return;

    setIsSubmitting(true);
    try {
      // Convert key-value array to update data
      const settingsObject: Record<string, string> = {};
      settingsData.forEach(({ key, value }) => {
        if (key.trim()) {
          settingsObject[key.trim()] = value;
        }
      });

      const updateData: UpdatePaymentProviderSettingsDto = {
        apiUrl: settingsObject.apiUrl || settingsObject.ApiUrl || '',
        apiKey: settingsObject.apiKey || settingsObject.ApiKey || '',
        secretKey: settingsObject.secretKey || settingsObject.SecretKey || '',
        logoUrl: settingsObject.logoUrl || settingsObject.LogoUrl || '',
        sortOrder: parseInt(settingsObject.sortOrder || settingsObject.SortOrder || '0'),
        isActive: settingsObject.isActive === 'true' || settingsObject.IsActive === 'true'
      };

      console.log('Submitting payment provider update data:', updateData);

      await updateSettingsMutation.mutateAsync({
        id: provider.id,
        data: updateData
      });

      onOpenChange(false);
    } catch (error) {
      console.error('Failed to update settings:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  if (isLoadingSettings) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[600px]">
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">{commonT('loading')}</span>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {provider.logoUrl && (
              <img
                src={provider.logoUrl}
                alt={provider.name}
                className="h-8 w-8 object-contain rounded"
              />
            )}
            {t('settingsDialog.title', { name: provider.name })}
            <Badge variant={provider.isImplemented ? "default" : "secondary"}>
              {provider.isImplemented ? t('status.implemented') : t('status.notImplemented')}
            </Badge>
          </DialogTitle>
          <DialogDescription>
            {t('settingsDialog.description')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {t('settingsDialog.keyValueSettings')}
                {canUpdate && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleAddSetting}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    {t('actions.addSetting')}
                  </Button>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {settingsData.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  {t('messages.noSettings')}
                </div>
              ) : (
                <div className="space-y-3">
                  {settingsData.map((setting, index) => (
                    <div key={index} className="grid grid-cols-12 gap-2 items-center">
                      <div className="col-span-4">
                        <Input
                          placeholder={t('placeholders.settingKey')}
                          value={setting.key}
                          onChange={(e) => handleSettingChange(index, 'key', e.target.value)}
                          disabled={!canUpdate}
                          className={setting.isNew ? 'border-blue-300' : ''}
                        />
                      </div>
                      <div className="col-span-7">
                        <Input
                          placeholder={t('placeholders.settingValue')}
                          value={setting.value}
                          onChange={(e) => handleSettingChange(index, 'value', e.target.value)}
                          disabled={!canUpdate}
                          className={setting.isNew ? 'border-blue-300' : ''}
                        />
                      </div>
                      <div className="col-span-1">
                        {canUpdate && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveSetting(index)}
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Info Card */}
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="pt-6">
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-2">{t('help.settingsInfo')}</p>
                <ul className="list-disc list-inside space-y-1 text-blue-700">
                  <li>{t('help.keyValueFormat')}</li>
                  <li>{t('help.commonKeys')}</li>
                  <li>{t('help.booleanValues')}</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={isSubmitting}
          >
            <X className="h-4 w-4 mr-2" />
            {commonT('actions.cancel')}
          </Button>
          {canUpdate && (
            <Button
              type="button"
              onClick={handleSubmit}
              disabled={isSubmitting || !hasChanges}
            >
              {isSubmitting ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              {commonT('actions.save')}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
