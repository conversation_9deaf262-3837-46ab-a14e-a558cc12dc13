'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Search,
  CreditCard,
  CheckCircle,
  XCircle,
  Eye,
  Edit,
  TestTube,
  Shield
} from 'lucide-react';
import {
  usePaymentProviders,
  useTogglePaymentProviderStatus,
  useValidatePaymentProviderSettings,
  useCheckPaymentProviderAvailability
} from '@/hooks/usePaymentProviders';
import type { PaymentProviderDto } from '@/types/payment-provider';
import PaymentProviderSettingsDialog from './PaymentProviderSettingsDialog';

interface PaymentProvidersListClientProps {
  canCreate: boolean;
  canUpdate: boolean;
  canDelete: boolean;
}

export default function PaymentProvidersListClient({
  canCreate,
  canUpdate,
  canDelete
}: PaymentProvidersListClientProps) {
  const t = useTranslations('payment.providers');
  const commonT = useTranslations('common');

  const [searchTerm, setSearchTerm] = useState('');
  const [showImplementedOnly, setShowImplementedOnly] = useState(false);
  const [showActiveOnly, setShowActiveOnly] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<PaymentProviderDto | null>(null);
  const [isSettingsDialogOpen, setIsSettingsDialogOpen] = useState(false);

  const { providers, isLoading, error } = usePaymentProviders({
    search: searchTerm || undefined,
    isImplemented: showImplementedOnly || undefined,
    isActive: showActiveOnly || undefined,
  });

  const toggleStatusMutation = useTogglePaymentProviderStatus();
  const validateSettingsMutation = useValidatePaymentProviderSettings();
  const checkAvailabilityMutation = useCheckPaymentProviderAvailability();

  const handleToggleStatus = async (provider: PaymentProviderDto) => {
    await toggleStatusMutation.mutateAsync(provider.id);
  };

  const handleValidateSettings = async (provider: PaymentProviderDto) => {
    await validateSettingsMutation.mutateAsync(provider.id);
  };

  const handleCheckAvailability = async (provider: PaymentProviderDto) => {
    await checkAvailabilityMutation.mutateAsync(provider.id);
  };

  const handleEditSettings = (provider: PaymentProviderDto) => {
    setSelectedProvider(provider);
    setIsSettingsDialogOpen(true);
  };
  console.log(providers)
  const filteredProviders = providers?.filter(provider => {
    const matchesSearch = !searchTerm ||
      provider.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      provider.shortCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
      provider.description?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesImplemented = !showImplementedOnly || provider.isImplemented;
    const matchesActive = !showActiveOnly || provider.isActive;

    return matchesSearch && matchesImplemented && matchesActive;
  }) || [];

  if (isLoading) {
    return <PaymentProvidersListSkeleton />;
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <XCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <p className="text-muted-foreground">{t('messages.loadingProviders')}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <div className="space-y-6">
        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              {commonT('filters')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder={t('placeholders.searchProviders')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="flex gap-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="implemented-only"
                    checked={showImplementedOnly}
                    onCheckedChange={setShowImplementedOnly}
                  />
                  <label htmlFor="implemented-only" className="text-sm">
                    {t('implementedOnly')}
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="active-only"
                    checked={showActiveOnly}
                    onCheckedChange={setShowActiveOnly}
                  />
                  <label htmlFor="active-only" className="text-sm">
                    {t('activeOnly')}
                  </label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Providers List */}
        <div className="grid gap-4">
          {filteredProviders.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">{t('noProviders')}</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            filteredProviders.map((provider) => (
              <Card key={provider.id}>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      {provider.logoUrl && (
                        <img
                          src={provider.logoUrl}
                          alt={provider.name}
                          className="h-12 w-12 object-contain rounded"
                        />
                      )}
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold">{provider.name}</h3>
                          <Badge variant="outline">{provider.shortCode}</Badge>
                          <Badge variant={provider.isImplemented ? "default" : "secondary"}>
                            {provider.isImplemented ? t('status.implemented') : t('status.notImplemented')}
                          </Badge>
                          <Badge variant={provider.isActive ? "default" : "secondary"}>
                            {provider.isActive ? t('status.active') : t('status.inactive')}
                          </Badge>
                        </div>
                        {provider.description && (
                          <p className="text-sm text-muted-foreground mt-1">
                            {provider.description}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      {canUpdate && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditSettings(provider)}
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            {t('actions.editSettings')}
                          </Button>

                          <Button
                            variant={provider.isActive ? "destructive" : "default"}
                            size="sm"
                            onClick={() => handleToggleStatus(provider)}
                            disabled={toggleStatusMutation.isPending}
                          >
                            {provider.isActive ? t('actions.deactivate') : t('actions.activate')}
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>

      {/* Settings Dialog */}
      {selectedProvider && (
        <PaymentProviderSettingsDialog
          provider={selectedProvider}
          open={isSettingsDialogOpen}
          onOpenChange={setIsSettingsDialogOpen}
          canUpdate={canUpdate}
        />
      )}
    </>
  );
}

function PaymentProvidersListSkeleton() {
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-10 w-full" />
          <div className="flex gap-4">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-6 w-24" />
          </div>
        </CardContent>
      </Card>

      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <Skeleton className="h-12 w-12 rounded" />
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-6 w-32" />
                      <Skeleton className="h-5 w-16" />
                      <Skeleton className="h-5 w-20" />
                      <Skeleton className="h-5 w-16" />
                    </div>
                    <Skeleton className="h-4 w-64" />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Skeleton className="h-8 w-24" />
                  <Skeleton className="h-8 w-24" />
                  <Skeleton className="h-8 w-24" />
                  <Skeleton className="h-8 w-20" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
