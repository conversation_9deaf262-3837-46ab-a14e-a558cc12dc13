export interface ProductAttributeDto {
  id: string;
  name: string;
  shortName: string;
  isVariantAttribute: boolean;
  isListAttribute: boolean;
  isActive: boolean;
  categories: { id: string; name: string }[];
  values: ProductAttributeValueDto[];
}

export interface ProductAttributeCreateDto {
  name: string;
  shortName: string;
  isVariantAttribute: boolean;
  isListAttribute: boolean;
  categoryIds: string[];
}

export interface ProductAttributeUpdateDto {
  id: string;
  name: string;
  shortName: string;
  isVariantAttribute: boolean;
  isListAttribute: boolean;
  categoryIds: string[];
}

export interface ProductAttributeFilterParams {
  search?: string;
}

export interface ProductAttributeValueDto {
  id: string;
  attributeId: string;
  value: string;
  isActive: boolean;
  attribute?: ProductAttributeDto;
}

export interface ProductAttributeValueCreateDto {
  attributeId: string;
  value: string;
}

export interface ProductAttributeValueUpdateDto {
  id: string;
  attributeId: string;
  value: string;
}
