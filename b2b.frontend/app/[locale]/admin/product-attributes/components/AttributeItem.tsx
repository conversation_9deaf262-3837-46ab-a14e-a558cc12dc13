'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { ProductAttributeDto, ProductAttributeValueDto } from '../types';
import { CategoryDto } from '../../product-categories/types';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronRight, Edit, PlusCircleIcon, Trash2 } from 'lucide-react';
import { TableRow, TableCell } from '@/components/ui/table';
import { useProductAttributeValues, useDeleteProductAttributeValue } from '@/lib/api/hooks/useProductAttributeValues';
import { useDeleteProductAttribute } from '@/lib/api/hooks/useProductAttributes';
import { Dialog, DialogContent, DialogHeader, DialogFooter, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import AttributeValueModal from './AttributeValueModal';

interface AttributeItemProps {
  attribute: ProductAttributeDto;
  expandedAttributes: string[];
  toggleAttributeExpansion: (attributeId: string) => void;
  searchTerm?: string;
  categories: CategoryDto[];
  canRead: boolean;
  canCreate: boolean;
  canUpdate: boolean;
  canDelete: boolean;
}

export default function AttributeItem({
  attribute,
  expandedAttributes,
  toggleAttributeExpansion,
  searchTerm,
  categories,
  canRead,
  canCreate,
  canUpdate,
  canDelete
}: AttributeItemProps) {
  const commonT = useTranslations('common');
  const t = useTranslations('productAttribute');
  const router = useRouter();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [forceDeleteDialogOpen, setForceDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [deleteType, setDeleteType] = useState<'attribute' | 'value'>('attribute');

  // Modal states
  const [valueModalOpen, setValueModalOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState<ProductAttributeValueDto | null>(null);

  const deleteAttributeValue = useDeleteProductAttributeValue();
  const deleteAttribute = useDeleteProductAttribute();

  // Fetch attribute values when expanded
  const { data: attributeValues = [], isLoading } = useProductAttributeValues(
    expandedAttributes.includes(attribute.id) ? attribute.id : undefined
  );

  // Check if this attribute matches the search term
  const matchesSearch = (term?: string): boolean => {
    if (!term) return true;
    const termLower = term.toLowerCase();
    return attribute.name.toLowerCase().includes(termLower) ||
      attribute.shortName.toLowerCase().includes(termLower);
  };

  // Handle delete click for attribute or attribute value
  const handleDeleteClick = (id: string, type: 'attribute' | 'value') => {
    setDeleteId(id);
    setDeleteType(type);
    setDeleteDialogOpen(true);
  };

  // Handle edit click for attribute or attribute value
  const handleEditClick = (id: string, type: 'attribute' | 'value') => {
    if (type === 'attribute') {
      // Nitelik düzenleme sayfasına yönlendir
      router.push(`/admin/product-attributes/edit/${id}`);
    } else {
      const value = attributeValues.find(v => v.id === id);
      if (value) {
        setSelectedValue(value);
        setValueModalOpen(true);
      }
    }
  };

  // Handle add value click
  const handleAddValueClick = (_attributeId: string) => {
    setSelectedValue(null);
    setValueModalOpen(true);
  };

  // Handle delete confirm
  const handleDeleteConfirm = () => {
    console.log('deleteId', deleteId);
    if (!deleteId) return;

    if (deleteType === 'value') {
      deleteAttributeValue.mutate(deleteId, {
        onSuccess: () => {
          setDeleteDialogOpen(false);
          router.refresh();
        },
      });
    } else if (deleteType === 'attribute') {
      deleteAttribute.mutate({ id: deleteId }, {
        onSuccess: () => {
          setDeleteDialogOpen(false);
          router.refresh();
        },
        onError: (error: any) => {
          console.error('Delete error:', error);
          // Handle specific error messages
          if (error?.response?.data?.includes('kullanan ürünler mevcut')) {
            setDeleteDialogOpen(false);
            setForceDeleteDialogOpen(true);
          } else {
            alert(t('deleteError'));
          }
        },
      });
    }
  };

  // Handle force delete confirm
  const handleForceDeleteConfirm = () => {
    if (!deleteId) return;

    deleteAttribute.mutate({ id: deleteId, force: true }, {
      onSuccess: () => {
        setForceDeleteDialogOpen(false);
        router.refresh();
      },
      onError: (error: any) => {
        console.error('Force delete error:', error);
        alert(t('deleteError'));
      },
    });
  };

  // If there's a search term and this attribute doesn't match, don't render
  if (searchTerm && !matchesSearch(searchTerm)) {
    return null;
  }

  return (
    <>
      <TableRow>
        <TableCell>
          <div className="flex items-center">
            {/* Expand/collapse button */}
            <Button
              variant="ghost"
              size="sm"
              className="p-0 h-6 w-6 mr-2"
              onClick={() => toggleAttributeExpansion(attribute.id)}
            >
              {expandedAttributes.includes(attribute.id) ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
            <span className={`font-medium ${!attribute.isActive ? 'line-through text-gray-500' : ''}`}>
              {attribute.name}
            </span>
          </div>
        </TableCell>
        <TableCell className={!attribute.isActive ? 'line-through text-gray-500' : ''}>
          {attribute.shortName}
        </TableCell>
        <TableCell className={!attribute.isActive ? 'line-through text-gray-500' : ''}>
          {attribute.isVariantAttribute ? t('yes') : t('no')}
        </TableCell>
        <TableCell className={!attribute.isActive ? 'line-through text-gray-500' : ''}>
          {attribute.isListAttribute ? t('yes') : t('no')}
        </TableCell>
        <TableCell>
          <div className="flex items-center justify-end space-x-1">
            {canCreate && (
              <Button
                variant="ghost"
                size="sm"
                className="text-green-600"
                onClick={() => handleAddValueClick(attribute.id)}
              >
                <PlusCircleIcon className="h-4 w-4" />
              </Button>
            )}
            {canUpdate && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleEditClick(attribute.id, 'attribute')}
              >
                <Edit className="h-4 w-4" />
              </Button>
            )}
            {canDelete && (
              <Button
                variant="ghost"
                size="sm"
                className="text-red-600"
                onClick={() => handleDeleteClick(attribute.id, 'attribute')}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        </TableCell>
      </TableRow>

      {/* Render attribute values if expanded */}
      {expandedAttributes.includes(attribute.id) && (
        isLoading ? (
          <TableRow>
            <TableCell colSpan={5} className="text-center py-2">
              {t('loading')}
            </TableCell>
          </TableRow>
        ) : attributeValues.length > 0 ? (
          attributeValues.map((value) => (
            <TableRow key={value.id} className="bg-muted/30">
              <TableCell className={`pl-8 ${!value.isActive ? 'line-through text-gray-500' : ''}`}>
                {value.value}
              </TableCell>
              <TableCell colSpan={3}></TableCell>
              <TableCell>
                <div className="flex items-center justify-end space-x-1">
                  {canUpdate && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditClick(value.id, 'value')}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  )}
                  {canDelete && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-600"
                      onClick={() => handleDeleteClick(value.id, 'value')}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))
        ) : (
          <TableRow className="bg-muted/30">
            <TableCell colSpan={5} className="text-center py-2">
              {t('noAttributeValues')}
            </TableCell>
          </TableRow>
        )
      )}

      {/* Modal for delete confirmation */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {deleteType === 'attribute' ? t('deleteAttributeConfirmTitle') : t('deleteValueConfirmTitle')}
            </DialogTitle>
            <DialogDescription>
              {deleteType === 'attribute' ? t('deleteAttributeConfirmMessage') : t('deleteValueConfirmMessage')}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button onClick={() => setDeleteDialogOpen(false)}>
              {commonT('actions.cancel')}
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={deleteAttributeValue.isPending}
            >
              {commonT('actions.delete')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal for force delete confirmation */}
      <Dialog open={forceDeleteDialogOpen} onOpenChange={setForceDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('forceDeleteTitle')}</DialogTitle>
            <DialogDescription>
              <div className="space-y-2">
                <p>{t('forceDeleteMessage')}</p>
                <p className="text-orange-600 font-medium">{t('forceDeleteWarning')}</p>
              </div>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button onClick={() => setForceDeleteDialogOpen(false)}>
              {t('cancelDelete')}
            </Button>
            <Button
              variant="destructive"
              onClick={handleForceDeleteConfirm}
              disabled={deleteAttribute.isPending}
            >
              {t('continueDelete')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal for editing/adding attribute values */}
      <AttributeValueModal
        attributeId={attribute.id}
        value={selectedValue}
        isOpen={valueModalOpen}
        onClose={() => {
          setValueModalOpen(false);
          setSelectedValue(null);
          router.refresh();
        }}
      />


    </>
  );
}
