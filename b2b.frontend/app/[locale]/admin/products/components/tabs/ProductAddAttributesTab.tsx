"use client"

import React, { useState, useEffect } from "react"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Badge } from "@/components/ui/badge"
import { Settings, AlertCircle } from "lucide-react"
import { useTranslations } from "next-intl"
import { useAllAttributes } from "@/lib/api/hooks/useProductAttributes"
import type { ProductAttributeMappingCreateDto } from "@/lib/api/hooks/useProductAttributeMappings"

interface ProductAddAttributesTabProps {
    categoryId: string;
    attributeMappings: ProductAttributeMappingCreateDto[];
    updateAttributeMappings: (mappings: ProductAttributeMappingCreateDto[]) => void;
    productType?: number; // 0: Simple, 1: Variant, 2: Grouped
}

export function ProductAddAttributesTab({ categoryId, attributeMappings, updateAttributeMappings, productType = 0 }: ProductAddAttributesTabProps) {
    const t = useTranslations("product");

    // Fetch ALL attributes (variant + list) for the selected category
    const { data: attributes, isLoading, error } = useAllAttributes(categoryId);

    // Local state for selected attribute values
    const [selectedValues, setSelectedValues] = useState<Record<string, string[]>>({});

    // Separate attributes into product attributes (IsListAttribute) and variant attributes (IsVariantAttribute)
    const productAttributes = attributes?.filter((attr) => attr.isListAttribute && !attr.isVariantAttribute) || [];
    const variantAttributes = attributes?.filter((attr) => attr.isVariantAttribute) || [];

    // Initialize selected values from existing mappings
    useEffect(() => {
        if (attributeMappings && attributeMappings.length > 0) {
            const valuesByAttribute: Record<string, string[]> = {};
            attributeMappings.forEach((mapping) => {
                if (!valuesByAttribute[mapping.attributeId]) {
                    valuesByAttribute[mapping.attributeId] = [];
                }
                valuesByAttribute[mapping.attributeId].push(mapping.attributeValueId);
            });
            setSelectedValues(valuesByAttribute);
        }
    }, [attributeMappings]);

    // Handle attribute value selection (for variant attributes - multiple selection)
    const handleValueChange = (attributeId: string, valueId: string, checked: boolean) => {
        setSelectedValues((prev) => {
            const current = prev[attributeId] || [];
            const updated = checked ? [...current, valueId] : current.filter((id) => id !== valueId);

            return { ...prev, [attributeId]: updated };
        });
    };

    // Handle product attribute selection (for product attributes - single selection)
    const handleProductAttributeChange = (attributeId: string, valueId: string) => {
        setSelectedValues((prev) => {
            if (valueId === "undefined") {
                // Remove the attribute from selected values (clear selection)
                const updated = { ...prev };
                delete updated[attributeId];
                return updated;
            } else {
                return {
                    ...prev,
                    [attributeId]: [valueId], // Single selection
                };
            }
        });
    };

    // Update attribute mappings when selected values change
    useEffect(() => {
        const newMappings: ProductAttributeMappingCreateDto[] = [];
        Object.entries(selectedValues).forEach(([attrId, valueIds]) => {
            valueIds.forEach((valueId) => {
                newMappings.push({
                    attributeId: attrId,
                    attributeValueId: valueId,
                });
            });
        });

        // Only update if mappings actually changed
        const currentMappingsString = JSON.stringify(attributeMappings.sort((a, b) => a.attributeId.localeCompare(b.attributeId) || a.attributeValueId.localeCompare(b.attributeValueId)));
        const newMappingsString = JSON.stringify(newMappings.sort((a, b) => a.attributeId.localeCompare(b.attributeId) || a.attributeValueId.localeCompare(b.attributeValueId)));

        if (currentMappingsString !== newMappingsString) {
            updateAttributeMappings(newMappings);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedValues]); // updateAttributeMappings intentionally omitted to prevent infinite loops

    // Check if a value is selected
    const isValueSelected = (attributeId: string, valueId: string) => {
        return selectedValues[attributeId]?.includes(valueId) || false;
    };

    if (!categoryId) {
        return (
            <div className="text-center py-12">
                <AlertCircle className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Kategori Seçin</h3>
                <p className="text-gray-500">Ürün niteliklerini görmek için önce bir kategori seçin.</p>
            </div>
        );
    }

    if (isLoading) {
        return (
            <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-gray-500">Nitelikler yükleniyor...</p>
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center py-12">
                <AlertCircle className="mx-auto h-12 w-12 text-red-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Hata</h3>
                <p className="text-gray-500">Nitelikler yüklenirken bir hata oluştu.</p>
            </div>
        );
    }

    if (!attributes || attributes.length === 0) {
        return (
            <div className="text-center py-12">
                <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Nitelik Bulunamadı</h3>
                <p className="text-gray-500">Bu kategori için tanımlanmış nitelik bulunmuyor.</p>
            </div>
        );
    }

    if (productAttributes.length === 0 && variantAttributes.length === 0) {
        return (
            <div className="text-center py-12">
                <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Kullanılabilir Nitelik Yok</h3>
                <p className="text-gray-500">Bu kategori için ürün özellikleri veya varyant nitelikleri tanımlanmamış.</p>
            </div>
        );
    }
    console.log(attributes);
    return (
        <div className="space-y-8">
            <div>
                <h3 className="text-lg font-medium">{t("tabs.attributes") || "Ürün Nitelikleri"}</h3>
                <p className="text-sm text-muted-foreground">Ürün özellikleri tüm varyantlarda aynı olacak, varyant özellikleri ise farklı kombinasyonlar oluşturacaktır.</p>
            </div>

            {/* Ürün Özellikleri (IsListAttribute=true) */}
            {productAttributes.length > 0 && (
                <div className="space-y-4">
                    <div className="border-b pb-2">
                        <h4 className="text-base font-medium text-gray-900">Ürün Özellikleri</h4>
                        <p className="text-sm text-gray-500">Bu özellikler tüm varyantlarda aynı olacaktır. Her özellik için tek bir değer seçin.</p>
                    </div>
                    <div className="grid gap-4">
                        {productAttributes.map((attribute) => (
                            <Card key={attribute.id} className="border-green-200">
                                <CardHeader className="pb-3">
                                    <div className="flex items-center justify-between">
                                        <CardTitle className="text-base font-medium">
                                            {attribute.name}
                                            <Badge variant="outline" className="ml-2 bg-green-50 text-green-700 border-green-200">
                                                Ürün Özelliği
                                            </Badge>
                                        </CardTitle>
                                        <span className="text-sm text-muted-foreground">
                                            {selectedValues[attribute.id]?.length || 0}/1 seçili
                                            {(!selectedValues[attribute.id] || selectedValues[attribute.id].length === 0) && (
                                                <span className="ml-1 text-gray-400 italic">({t("attributes.none")})</span>
                                            )}
                                        </span>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    {attribute.values && attribute.values.length > 0 ? (
                                        <RadioGroup value={selectedValues[attribute.id]?.[0] || ""} onValueChange={(value) => handleProductAttributeChange(attribute.id, value)}>
                                            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                                                {/* Tanımlanmamış option */}
                                                <div className="flex items-center space-x-2">
                                                    <RadioGroupItem value="undefined" id={`product-${attribute.id}-undefined`} />
                                                    <Label htmlFor={`product-${attribute.id}-undefined`} className="text-sm font-normal cursor-pointer text-gray-500 italic">
                                                        {t("attributes.none")}
                                                    </Label>
                                                </div>
                                                {attribute.values.map((value) => (
                                                    <div key={value.id} className="flex items-center space-x-2">
                                                        <RadioGroupItem value={value.id} id={`product-${attribute.id}-${value.id}`} />
                                                        <Label htmlFor={`product-${attribute.id}-${value.id}`} className="text-sm font-normal cursor-pointer">
                                                            {value.value}
                                                        </Label>
                                                    </div>
                                                ))}
                                            </div>
                                        </RadioGroup>
                                    ) : (
                                        <p className="text-sm text-muted-foreground italic">Bu nitelik için değer tanımlanmamış.</p>
                                    )}
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>
            )}

            {/* Varyant Özellikleri (IsVariantAttribute=true) - Sadece varyantlı ürünlerde görünür */}
            {variantAttributes.length > 0 && productType === 1 && (
                <div className="space-y-4">
                    <div className="border-b pb-2">
                        <h4 className="text-base font-medium text-gray-900">Varyant Özellikleri</h4>
                        <p className="text-sm text-gray-500">Bu özellikler varyant kombinasyonları oluşturacaktır. Her özellik için birden fazla değer seçebilirsiniz.</p>
                    </div>
                    <div className="grid gap-4">
                        {variantAttributes.map((attribute) => (
                            <Card key={attribute.id} className="border-blue-200">
                                <CardHeader className="pb-3">
                                    <div className="flex items-center justify-between">
                                        <CardTitle className="text-base font-medium">
                                            {attribute.name}
                                            <Badge variant="secondary" className="ml-2 bg-blue-50 text-blue-700 border-blue-200">
                                                Varyant
                                            </Badge>
                                        </CardTitle>
                                        <span className="text-sm text-muted-foreground">{selectedValues[attribute.id]?.length || 0} seçili</span>
                                    </div>
                                    {attribute.shortName && <p className="text-sm text-muted-foreground">{attribute.shortName}</p>}
                                </CardHeader>
                                <CardContent>
                                    {attribute.values && attribute.values.length > 0 ? (
                                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                                            {attribute.values.map((value) => (
                                                <div key={value.id} className="flex items-center space-x-2">
                                                    <Checkbox id={`variant-${attribute.id}-${value.id}`} checked={isValueSelected(attribute.id, value.id)} onCheckedChange={(checked) => handleValueChange(attribute.id, value.id, checked === true)} />
                                                    <Label htmlFor={`variant-${attribute.id}-${value.id}`} className="text-sm font-normal cursor-pointer">
                                                        {value.value}
                                                    </Label>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <p className="text-sm text-muted-foreground italic">Bu nitelik için değer tanımlanmamış.</p>
                                    )}
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>
            )}

            {/* Summary */}
            {attributeMappings && attributeMappings.length > 0 && (
                <Card className="">
                    <CardHeader className="pb-3">
                        <CardTitle className="text-base font-medium">Seçilen Nitelikler Özeti</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3">
                            {/* Product Attributes Summary */}
                            {productAttributes.some((attr) => selectedValues[attr.id]?.length > 0) && (
                                <div>
                                    <h5 className="text-sm font-medium mb-2">Ürün Özellikleri:</h5>
                                    <div className="flex flex-wrap gap-2">
                                        {productAttributes.map((attribute) => {
                                            const selectedCount = selectedValues[attribute.id]?.length || 0;
                                            if (selectedCount === 0) return null;

                                            return (
                                                <Badge key={attribute.id} variant="outline" className="bg-green-50 text-green-700 border-green-200">
                                                    {attribute.name}: {selectedCount} değer
                                                </Badge>
                                            );
                                        })}
                                    </div>
                                </div>
                            )}

                            {/* Variant Attributes Summary - Sadece varyantlı ürünlerde görünür */}
                            {variantAttributes.some((attr) => selectedValues[attr.id]?.length > 0) && productType === 1 && (
                                <div>
                                    <h5 className="text-sm font-medium mb-2">Varyant Özellikleri:</h5>
                                    <div className="flex flex-wrap gap-2">
                                        {variantAttributes.map((attribute) => {
                                            const selectedCount = selectedValues[attribute.id]?.length || 0;
                                            if (selectedCount === 0) return null;

                                            return (
                                                <Badge key={attribute.id} variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                                                    {attribute.name}: {selectedCount} değer
                                                </Badge>
                                            );
                                        })}
                                    </div>
                                </div>
                            )}
                        </div>
                        <p className="text-sm mt-3 pt-3 border-t">Toplam {attributeMappings.length} nitelik değeri seçildi.</p>
                    </CardContent>
                </Card>
            )}
        </div>
    );
}
