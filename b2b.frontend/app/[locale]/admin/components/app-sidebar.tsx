"use client"

import * as React from "react"
import {
  Command,
  Frame,
  LifeBuoy,
  Mail,
  Map,
  Megaphone,
  PieChart,
  Send,
  Settings2,
  ShoppingCart,
  Shirt,
  User,
  Users,
  BarChart3
} from "lucide-react"
import { useSession } from "next-auth/react"

import { NavMain } from "@/app/[locale]/admin/components/nav-main"
import { NavProjects } from "@/app/[locale]/admin/components/nav-projects"
import { NavSecondary } from "@/app/[locale]/admin/components/nav-secondary"
import { NavUser } from "@/app/[locale]/admin/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { VisuallyHidden } from "@radix-ui/react-visually-hidden"
import { useTranslations } from "next-intl"
import { useMediaQuery } from '@/hooks/use-mobile';
import { Sheet, Sheet<PERSON>ontent, Sheet<PERSON><PERSON>le, SheetTrigger } from '@/components/ui/sheet';



export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { data: session } = useSession()
  const t = useTranslations('common');
  const isMobile = useMediaQuery('(max-width: 768px)');
  const [open, setOpen] = React.useState(false);

  const staticData = {
    navMain: [
      {
        title: t("navigation.products"),
        url: "#",
        icon: Shirt,
        isActive: true,
        items: [
          {
            title: t("navigation.productlist"),
            url: "/admin/products",
          },
          {
            title: t("navigation.productcategories"),
            url: "/admin/product-categories",
          },
          {
            title: t("navigation.productbrands"),
            url: "/admin/product-brands",
          },
          {
            title: t("navigation.productattributes"),
            url: "/admin/product-attributes",
          },
          {
            title: t("navigation.productreviews"),
            url: "/admin/product-reviews",
          },
        ],
      },
      {
        title: t("navigation.orders"),
        url: "#",
        icon: ShoppingCart,
        items: [
          {
            title: t("navigation.orderslist"),
            url: "/admin/orders",
          },
          {
            title: t("navigation.payments"),
            url: "/admin/payments",
          },
          {
            title: t("navigation.shipments"),
            url: "/admin/shipments",
          },
        ],
      },
      {
        title: t("navigation.users"),
        url: "#",
        icon: User,
        items: [
          {
            title: t("navigation.userlist"),
            url: "/admin/users",
          },
          {
            title: t("navigation.adduser"),
            url: "/admin/users/add",
          },
          {
            title: t("navigation.roles"),
            url: "/admin/roles",
          },
        ],
      },
      {
        title: t("navigation.customers"),
        url: "#",
        icon: Users,
        items: [
          {
            title: t("navigation.customerlist"),
            url: "/admin/customers",
          },
          {
            title: t("navigation.addcustomer"),
            url: "/admin/customers/add",
          },
        ],
      },
      {
        title: t("navigation.campaigns"),
        url: "#",
        icon: Megaphone,
        items: [
          {
            title: t("navigation.campaignlist"),
            url: "/admin/campaigns",
          },
          {
            title: t("navigation.addcampaign"),
            url: "/admin/campaigns/add",
          },
          {
            title: t("navigation.coupons"),
            url: "/admin/campaigns/coupons",
          },
        ],
      },
      {
        title: t("navigation.reports"),
        url: "#",
        icon: BarChart3,
        items: [
          {
            title: t("navigation.customeranalytics"),
            url: "/admin/reports/customers",
          },
          {
            title: t("navigation.campaignanalytics"),
            url: "/admin/reports/campaigns",
          },
        ],
      },
      {
        title: t("navigation.mailtemplates"),
        url: "/admin/mail-templates",
        icon: Mail,
        items: [],
      },
      {
        title: t("navigation.settings"),
        url: "#",
        icon: Settings2,
        items: [
          {
            title: t("navigation.systemsettings"),
            url: "/admin/settings/systemsettings",
          },
          {
            title: t("navigation.shippingsettings"),
            url: "/admin/settings/shipping",
          },
          {
            title: t("navigation.paymentsettings"),
            url: "/admin/settings/payment-providers",
          },
        ],
      },
    ],
    navSecondary: [
      {
        title: "Support",
        url: "#",
        icon: LifeBuoy,
      },
      {
        title: "Feedback",
        url: "#",
        icon: Send,
      },
    ],
    projects: [
      {
        name: "Design Engineering",
        url: "#",
        icon: Frame,
      },
      {
        name: "Sales & Marketing",
        url: "#",
        icon: PieChart,
      },
      {
        name: "Travel",
        url: "#",
        icon: Map,
      },
    ],
  };
  // Kullanıcı bilgilerini session'dan al
  const userData = {
    name: session?.user?.name || "Kullanıcı",
    email: session?.user?.email || "<EMAIL>",
    avatar: "", // Avatar kullanmıyoruz, fallback kullanılacak
  }

  // Mobil header ve drawer
  if (isMobile) {
    return (
      <>
        {/* Fixed mobil üst bar */}
        <div className="fixed top-0 left-0 w-full h-14 z-100 bg-sidebar text-sidebar-foreground border-b flex items-center justify-between px-4">
          {/* Logo tam ortada, hamburger solda, sağda boşluk */}
          <SidebarMenu>
            <SidebarMenuItem onClick={() => setOpen(!open)}>
              <SidebarMenuButton size="lg" asChild>
                <a href="#">
                  <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                    <Command className="size-4" />
                  </div>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-medium">{process.env.NEXT_PUBLIC_COMPANY_NAME}</span>
                    <span className="truncate text-xs"></span>
                  </div>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
          <div className="w-10" /> {/* Sağda boşluk için */}
          <Sheet open={open} onOpenChange={setOpen}>
            <VisuallyHidden>
              <SheetTitle>Menü</SheetTitle>
            </VisuallyHidden>
            <SheetTrigger asChild>
              <button
                aria-label="Menüyü Aç"
                className="flex items-center justify-center size-10 rounded-md hover:bg-sidebar-accent focus:outline-none focus:ring-2 focus:ring-sidebar-accent"
                onClick={() => setOpen(true)}
              >
                <span className="sr-only">Menüyü Aç</span>
                <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-menu"><line x1="4" y1="12" x2="20" y2="12" /><line x1="4" y1="6" x2="20" y2="6" /><line x1="4" y1="18" x2="20" y2="18" /></svg>
              </button>
            </SheetTrigger>
            <SheetContent side="left" className="p-0 flex flex-col h-full w-72 max-w-full pt-14">
              {/* Drawer içeriği header'ın altında başlasın diye pt-14 */}
              <div className="flex-1 overflow-y-auto">
                <SidebarHeader>
                </SidebarHeader>
                <SidebarContent>
                  <NavMain items={staticData.navMain} />
                  {/* <NavProjects projects={staticData.projects} /> */}
                  {/* <NavSecondary items={staticData.navSecondary} className="mt-auto" /> */}
                </SidebarContent>
              </div>
              <SidebarFooter className="border-t bg-sidebar">
                <NavUser user={userData} />
              </SidebarFooter>
            </SheetContent>
          </Sheet>
        </div>
      </>
    );
  }

  // Masaüstü için mevcut sidebar
  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <a href="#">
                <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                  <Command className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-medium">{process.env.NEXT_PUBLIC_COMPANY_NAME}</span>
                  <span className="truncate text-xs"></span>
                </div>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={staticData.navMain} />
        {/* <NavProjects projects={staticData.projects} /> */}
        {/* <NavSecondary items={staticData.navSecondary} className="mt-auto" /> */}
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={userData} />
      </SidebarFooter>
    </Sidebar>
  )
}
