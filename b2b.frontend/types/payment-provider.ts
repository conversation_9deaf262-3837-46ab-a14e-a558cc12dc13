// Payment Provider types based on backend DTOs

export interface PaymentProviderDto {
  id: string;
  name: string;
  shortCode: string;
  description?: string;
  isImplemented: boolean;
  isActive: boolean;
  apiUrl?: string;
  apiKey?: string;
  secretKey?: string;
  logoUrl?: string;
  sortOrder: number;
}

export interface UpdatePaymentProviderSettingsDto {
  apiUrl?: string;
  apiKey?: string;
  secretKey?: string;
  logoUrl?: string;
  sortOrder: number;
  isActive: boolean;
}

// Filter parameters for API requests
export interface PaymentProviderFilterParams {
  isImplemented?: boolean;
  isActive?: boolean;
  search?: string;
}

// Form data for editing settings
export interface PaymentProviderFormData {
  apiUrl: string;
  apiKey: string;
  secretKey: string;
  logoUrl: string;
  sortOrder: number;
  isActive: boolean;
}

// API response types
export interface PaymentProviderResponse {
  data: PaymentProviderDto[];
  success: boolean;
  message?: string;
}

export interface PaymentProviderSingleResponse {
  data: PaymentProviderDto;
  success: boolean;
  message?: string;
}

export interface PaymentProviderSettingsResponse {
  data: Record<string, string>;
  success: boolean;
  message?: string;
}

export interface PaymentProviderValidationResponse {
  isValid: boolean;
  message: string;
}

export interface PaymentProviderAvailabilityResponse {
  isAvailable: boolean;
  message: string;
}

export interface PaymentProviderToggleResponse {
  isActive: boolean;
  message: string;
}
