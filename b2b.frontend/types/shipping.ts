// Shipping types based on backend DTOs

export interface ShippingCarrierDto {
  id: string;
  staticId: number;
  name: string;
  shortCode: string;
  description?: string;
  isImplemented: boolean;
  isActive: boolean;
  apiUrl?: string;
  logoUrl?: string;
  settings?: Record<string, string>;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

export interface UpdateShippingCarrierSettingsDto {
  settings: Record<string, string>;
  apiUrl?: string;
  apiKey?: string;
}

// Filter parameters for API requests
export interface ShippingCarrierFilterParams {
  isImplemented?: boolean;
  isActive?: boolean;
  search?: string;
}

// Form data for editing settings
export interface ShippingCarrierFormData {
  apiUrl: string;
  apiKey: string;
  logoUrl: string;
  sortOrder: number;
  isActive: boolean;
}

// API response types
export interface ShippingCarrierResponse {
  data: ShippingCarrierDto[];
  success: boolean;
  message?: string;
}

export interface ShippingCarrierSingleResponse {
  data: ShippingCarrierDto;
  success: boolean;
  message?: string;
}

export interface ShippingCarrierSettingsResponse {
  data: Record<string, string>;
  success: boolean;
  message?: string;
}

export interface ShippingCarrierValidationResponse {
  isValid: boolean;
  message: string;
}

export interface ShippingCarrierAvailabilityResponse {
  isAvailable: boolean;
  message: string;
}

export interface ShippingCarrierToggleResponse {
  isActive: boolean;
  message: string;
}
