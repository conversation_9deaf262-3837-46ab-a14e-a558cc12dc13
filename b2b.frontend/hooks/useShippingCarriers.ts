import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useTranslations } from 'next-intl';
import { api } from '@/lib/api/client';
import type {
  ShippingCarrierDto,
  UpdateShippingCarrierSettingsDto,
  ShippingCarrierFilterParams,
  ShippingCarrierValidationResponse,
  ShippingCarrierAvailabilityResponse,
  ShippingCarrierToggleResponse
} from '@/types/shipping';

// API Functions
const shippingCarrierApi = {
  // CRUD Operations
  getAll: async (filters?: ShippingCarrierFilterParams): Promise<ShippingCarrierDto[]> => {
    const params: Record<string, string> = {};
    if (filters?.isImplemented !== undefined) {
      params.isImplemented = filters.isImplemented.toString();
    }
    if (filters?.isActive !== undefined) {
      params.isActive = filters.isActive.toString();
    }
    if (filters?.search) {
      params.search = filters.search;
    }
    return api.get<ShippingCarrierDto[]>('/ShippingCarrier', params);
  },

  getById: async (id: string): Promise<ShippingCarrierDto> => {
    return api.get<ShippingCarrierDto>(`/ShippingCarrier/${id}`);
  },

  getAvailable: async (): Promise<ShippingCarrierDto[]> => {
    return api.get<ShippingCarrierDto[]>('/ShippingCarrier/available');
  },

  // Settings Management
  getSettings: async (id: string): Promise<Record<string, string>> => {
    return api.get<Record<string, string>>(`/ShippingCarrier/${id}/settings`);
  },

  updateSettings: async (id: string, data: UpdateShippingCarrierSettingsDto): Promise<void> => {
    return api.put(`/ShippingCarrier/${id}/settings`, data);
  },

  // Status Management
  toggleStatus: async (id: string): Promise<ShippingCarrierToggleResponse> => {
    return api.put<ShippingCarrierToggleResponse>(`/ShippingCarrier/${id}/toggle`);
  },

  // Validation and Testing
  validateSettings: async (id: string): Promise<ShippingCarrierValidationResponse> => {
    return api.post<ShippingCarrierValidationResponse>(`/ShippingCarrier/${id}/validate`);
  },

  checkAvailability: async (id: string): Promise<ShippingCarrierAvailabilityResponse> => {
    return api.get<ShippingCarrierAvailabilityResponse>(`/ShippingCarrier/${id}/available`);
  },
};

// React Query Hooks
export const useShippingCarriers = (filters?: ShippingCarrierFilterParams) => {
  const queryClient = useQueryClient();
  const t = useTranslations('shipping');

  // Queries
  const {
    data: carriers = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['shipping-carriers', filters],
    queryFn: () => shippingCarrierApi.getAll(filters),
  });

  const useShippingCarrier = (id: string) => useQuery({
    queryKey: ['shipping-carrier', id],
    queryFn: () => shippingCarrierApi.getById(id),
    enabled: !!id,
  });

  const useAvailableShippingCarriers = () => useQuery({
    queryKey: ['shipping-carriers', 'available'],
    queryFn: shippingCarrierApi.getAvailable,
  });

  const useShippingCarrierSettings = (id: string) => useQuery({
    queryKey: ['shipping-carrier-settings', id],
    queryFn: () => shippingCarrierApi.getSettings(id),
    enabled: !!id,
  });

  // Mutations
  const updateSettingsMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateShippingCarrierSettingsDto }) =>
      shippingCarrierApi.updateSettings(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['shipping-carriers'] });
      queryClient.invalidateQueries({ queryKey: ['shipping-carrier-settings'] });
      toast.success(t('messages.settingsUpdateSuccess'));
    },
    onError: (error: Error) => {
      toast.error(error.message || t('messages.settingsUpdateError'));
    },
  });

  const toggleStatusMutation = useMutation({
    mutationFn: shippingCarrierApi.toggleStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['shipping-carriers'] });
      toast.success(t('messages.statusUpdateSuccess'));
    },
    onError: (error: Error) => {
      toast.error(error.message || t('messages.statusUpdateError'));
    },
  });

  const validateSettingsMutation = useMutation({
    mutationFn: shippingCarrierApi.validateSettings,
    onSuccess: (data) => {
      if (data.isValid) {
        toast.success(t('messages.validationSuccess'));
      } else {
        toast.error(t('messages.validationError'));
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || t('messages.validationError'));
    },
  });

  const checkAvailabilityMutation = useMutation({
    mutationFn: shippingCarrierApi.checkAvailability,
    onSuccess: (data) => {
      if (data.isAvailable) {
        toast.success(t('messages.connectionSuccess'));
      } else {
        toast.error(t('messages.connectionError'));
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || t('messages.connectionError'));
    },
  });

  return {
    // Data
    carriers,
    isLoading,
    error,
    refetch,

    // Hooks
    useShippingCarrier,
    useAvailableShippingCarriers,
    useShippingCarrierSettings,

    // Mutations
    updateSettings: updateSettingsMutation.mutate,
    toggleStatus: toggleStatusMutation.mutate,
    validateSettings: validateSettingsMutation.mutate,
    checkAvailability: checkAvailabilityMutation.mutate,

    // Loading states
    isUpdatingSettings: updateSettingsMutation.isPending,
    isTogglingStatus: toggleStatusMutation.isPending,
    isValidatingSettings: validateSettingsMutation.isPending,
    isCheckingAvailability: checkAvailabilityMutation.isPending,

    // API functions for direct use
    api: shippingCarrierApi,
  };
};

// Individual hooks for backward compatibility
export const useShippingCarrier = (id: string) => {
  return useQuery({
    queryKey: ['shipping-carrier', id],
    queryFn: () => shippingCarrierApi.getById(id),
    enabled: !!id,
  });
};

export const useAvailableShippingCarriers = () => {
  return useQuery({
    queryKey: ['shipping-carriers', 'available'],
    queryFn: shippingCarrierApi.getAvailable,
  });
};

export const useShippingCarrierSettings = (id: string) => {
  return useQuery({
    queryKey: ['shipping-carrier-settings', id],
    queryFn: () => shippingCarrierApi.getSettings(id),
    enabled: !!id,
  });
};

export const useUpdateShippingCarrierSettings = () => {
  const queryClient = useQueryClient();
  const t = useTranslations('shipping');

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateShippingCarrierSettingsDto }) =>
      shippingCarrierApi.updateSettings(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['shipping-carriers'] });
      queryClient.invalidateQueries({ queryKey: ['shipping-carrier-settings'] });
      toast.success(t('messages.settingsUpdateSuccess'));
    },
    onError: (error: Error) => {
      toast.error(error.message || t('messages.settingsUpdateError'));
    },
  });
};

export const useToggleShippingCarrierStatus = () => {
  const queryClient = useQueryClient();
  const t = useTranslations('shipping');

  return useMutation({
    mutationFn: shippingCarrierApi.toggleStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['shipping-carriers'] });
      toast.success(t('messages.statusUpdateSuccess'));
    },
    onError: (error: Error) => {
      toast.error(error.message || t('messages.statusUpdateError'));
    },
  });
};

export const useValidateShippingCarrierSettings = () => {
  const t = useTranslations('shipping');

  return useMutation({
    mutationFn: shippingCarrierApi.validateSettings,
    onSuccess: (data) => {
      if (data.isValid) {
        toast.success(t('messages.validationSuccess'));
      } else {
        toast.error(t('messages.validationError'));
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || t('messages.validationError'));
    },
  });
};

export const useCheckShippingCarrierAvailability = () => {
  const t = useTranslations('shipping');

  return useMutation({
    mutationFn: shippingCarrierApi.checkAvailability,
    onSuccess: (data) => {
      if (data.isAvailable) {
        toast.success(t('messages.connectionSuccess'));
      } else {
        toast.error(t('messages.connectionError'));
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || t('messages.connectionError'));
    },
  });
};
