import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useTranslations } from 'next-intl';
import { api } from '@/lib/api/client';
import type {
  PaymentProviderDto,
  UpdatePaymentProviderSettingsDto,
  PaymentProviderFilterParams,
  PaymentProviderValidationResponse,
  PaymentProviderAvailabilityResponse,
  PaymentProviderToggleResponse
} from '@/types/payment-provider';

// API Functions
const paymentProviderApi = {
  // CRUD Operations
  getAll: async (filters?: PaymentProviderFilterParams): Promise<PaymentProviderDto[]> => {
    const params: Record<string, string> = {};
    if (filters?.isImplemented !== undefined) {
      params.isImplemented = filters.isImplemented.toString();
    }
    if (filters?.isActive !== undefined) {
      params.isActive = filters.isActive.toString();
    }
    if (filters?.search) {
      params.search = filters.search;
    }
    return api.get<PaymentProviderDto[]>('/PaymentProvider', params);
  },

  getById: async (id: string): Promise<PaymentProviderDto> => {
    return api.get<PaymentProviderDto>(`/PaymentProvider/${id}`);
  },

  getAvailable: async (): Promise<PaymentProviderDto[]> => {
    return api.get<PaymentProviderDto[]>('/PaymentProvider/available');
  },

  // Settings Management
  getSettings: async (id: string): Promise<Record<string, string>> => {
    return api.get<Record<string, string>>(`/PaymentProvider/${id}/settings`);
  },

  updateSettings: async (id: string, data: UpdatePaymentProviderSettingsDto): Promise<void> => {
    return api.put(`/PaymentProvider/${id}/settings`, data);
  },

  // Status Management
  toggleStatus: async (id: string): Promise<PaymentProviderToggleResponse> => {
    return api.put<PaymentProviderToggleResponse>(`/PaymentProvider/${id}/toggle`);
  },

  // Validation and Testing
  validateSettings: async (id: string): Promise<PaymentProviderValidationResponse> => {
    return api.post<PaymentProviderValidationResponse>(`/PaymentProvider/${id}/validate`);
  },

  checkAvailability: async (id: string): Promise<PaymentProviderAvailabilityResponse> => {
    return api.get<PaymentProviderAvailabilityResponse>(`/PaymentProvider/${id}/available`);
  },
};

// React Query Hooks
export const usePaymentProviders = (filters?: PaymentProviderFilterParams) => {
  const queryClient = useQueryClient();
  const t = useTranslations('payment.providers');

  // Queries
  const {
    data: providers = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['payment-providers', filters],
    queryFn: () => paymentProviderApi.getAll(filters),
  });

  const usePaymentProvider = (id: string) => useQuery({
    queryKey: ['payment-provider', id],
    queryFn: () => paymentProviderApi.getById(id),
    enabled: !!id,
  });

  const useAvailablePaymentProviders = () => useQuery({
    queryKey: ['payment-providers', 'available'],
    queryFn: paymentProviderApi.getAvailable,
  });

  const usePaymentProviderSettings = (id: string) => useQuery({
    queryKey: ['payment-provider-settings', id],
    queryFn: () => paymentProviderApi.getSettings(id),
    enabled: !!id,
  });

  // Mutations
  const updateSettingsMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdatePaymentProviderSettingsDto }) =>
      paymentProviderApi.updateSettings(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payment-providers'] });
      queryClient.invalidateQueries({ queryKey: ['payment-provider-settings'] });
      toast.success(t('messages.settingsUpdateSuccess'));
    },
    onError: (error: Error) => {
      toast.error(error.message || t('messages.settingsUpdateError'));
    },
  });

  const toggleStatusMutation = useMutation({
    mutationFn: paymentProviderApi.toggleStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payment-providers'] });
      toast.success(t('messages.statusUpdateSuccess'));
    },
    onError: (error: Error) => {
      toast.error(error.message || t('messages.statusUpdateError'));
    },
  });

  const validateSettingsMutation = useMutation({
    mutationFn: paymentProviderApi.validateSettings,
    onSuccess: (data) => {
      if (data.isValid) {
        toast.success(t('messages.validationSuccess'));
      } else {
        toast.error(t('messages.validationError'));
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || t('messages.validationError'));
    },
  });

  const checkAvailabilityMutation = useMutation({
    mutationFn: paymentProviderApi.checkAvailability,
    onSuccess: (data) => {
      if (data.isAvailable) {
        toast.success(t('messages.connectionSuccess'));
      } else {
        toast.error(t('messages.connectionError'));
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || t('messages.connectionError'));
    },
  });

  return {
    // Data
    providers,
    isLoading,
    error,
    refetch,

    // Hooks
    usePaymentProvider,
    useAvailablePaymentProviders,
    usePaymentProviderSettings,

    // Mutations
    updateSettings: updateSettingsMutation.mutate,
    toggleStatus: toggleStatusMutation.mutate,
    validateSettings: validateSettingsMutation.mutate,
    checkAvailability: checkAvailabilityMutation.mutate,

    // Loading states
    isUpdatingSettings: updateSettingsMutation.isPending,
    isTogglingStatus: toggleStatusMutation.isPending,
    isValidatingSettings: validateSettingsMutation.isPending,
    isCheckingAvailability: checkAvailabilityMutation.isPending,

    // API functions for direct use
    api: paymentProviderApi,
  };
};

// Individual hooks for backward compatibility
export const usePaymentProvider = (id: string) => {
  return useQuery({
    queryKey: ['payment-provider', id],
    queryFn: () => paymentProviderApi.getById(id),
    enabled: !!id,
  });
};

export const useAvailablePaymentProviders = () => {
  return useQuery({
    queryKey: ['payment-providers', 'available'],
    queryFn: paymentProviderApi.getAvailable,
  });
};

export const usePaymentProviderSettings = (id: string) => {
  return useQuery({
    queryKey: ['payment-provider-settings', id],
    queryFn: () => paymentProviderApi.getSettings(id),
    enabled: !!id,
  });
};

export const useUpdatePaymentProviderSettings = () => {
  const queryClient = useQueryClient();
  const t = useTranslations('payment.providers');

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdatePaymentProviderSettingsDto }) =>
      paymentProviderApi.updateSettings(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payment-providers'] });
      queryClient.invalidateQueries({ queryKey: ['payment-provider-settings'] });
      toast.success(t('messages.settingsUpdateSuccess'));
    },
    onError: (error: Error) => {
      toast.error(error.message || t('messages.settingsUpdateError'));
    },
  });
};

export const useTogglePaymentProviderStatus = () => {
  const queryClient = useQueryClient();
  const t = useTranslations('payment.providers');

  return useMutation({
    mutationFn: paymentProviderApi.toggleStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payment-providers'] });
      toast.success(t('messages.statusUpdateSuccess'));
    },
    onError: (error: Error) => {
      toast.error(error.message || t('messages.statusUpdateError'));
    },
  });
};

export const useValidatePaymentProviderSettings = () => {
  const t = useTranslations('payment.providers');

  return useMutation({
    mutationFn: paymentProviderApi.validateSettings,
    onSuccess: (data) => {
      if (data.isValid) {
        toast.success(t('messages.validationSuccess'));
      } else {
        toast.error(t('messages.validationError'));
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || t('messages.validationError'));
    },
  });
};

export const useCheckPaymentProviderAvailability = () => {
  const t = useTranslations('payment.providers');

  return useMutation({
    mutationFn: paymentProviderApi.checkAvailability,
    onSuccess: (data) => {
      if (data.isAvailable) {
        toast.success(t('messages.connectionSuccess'));
      } else {
        toast.error(t('messages.connectionError'));
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || t('messages.connectionError'));
    },
  });
};
