import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { getSession } from 'next-auth/react';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// Extend AxiosRequestConfig to include our custom property
interface ExtendedAxiosRequestConfig extends AxiosRequestConfig {
  _isApiResponseMethod?: boolean;
  _retry?: boolean;
}

// Custom error type for API responses
interface ApiError extends Error {
  response?: AxiosResponse;
  apiResponse?: any;
}

// Create a base axios instance with default configuration
export const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:33800/admin-api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth token
apiClient.interceptors.request.use(
  async (config) => {
    // Client-side (browser)
    if (typeof window !== 'undefined') {
      const session = await getSession();

      if (session?.accessToken) {
        config.headers.Authorization = `Bearer ${session.accessToken}`;
      }
    }
    // Server-side (Next.js server components/API routes)
    else {
      try {
        // Server-side session erişimi için getServerSession kullanın
        const session = await getServerSession(authOptions);

        if (session?.accessToken) {
          config.headers.Authorization = `Bearer ${session.accessToken}`;
        }
      } catch (error) {
        console.error('Server-side session error:', error);
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling common errors and ApiResponse format
apiClient.interceptors.response.use(
  (response) => {
    // Check if this is an ApiResponse method call
    const config = response.config as ExtendedAxiosRequestConfig;
    const isApiResponseMethod = response.config.url && config._isApiResponseMethod;

    // Handle ApiResponse format
    if (response.data && typeof response.data === 'object' && 'success' in response.data) {
      if (response.data.success) {
        // For ApiResponse methods, return full response
        if (isApiResponseMethod) {
          return response;
        }
        // For regular methods, return the data field for successful ApiResponse
        return { ...response, data: response.data.data };
      } else {
        // Throw error for failed ApiResponse
        const error = new Error(response.data.message || 'API Error') as ApiError;
        error.response = response;
        error.apiResponse = response.data;
        throw error;
      }
    }

    // Return original response if not ApiResponse format
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    // Handle 401 Unauthorized errors (token expired or backend restarted)
    if ((error.response?.status === 401 || error.status === 401) && !originalRequest._retry) {
      originalRequest._retry = true;
      // Use NextAuth signOut instead of direct redirect
      if (typeof window !== "undefined") {
        const { signOut } = await import("next-auth/react");
        const { toast } = await import("sonner");

        console.log("API 401 error, signing out...");
        toast.error("Oturumunuz sonlandırıldı. Lütfen tekrar giriş yapın.");

        await signOut({
          redirect: true,
          callbackUrl: "/login",
        });

        // Prevent further request retries
        return Promise.reject(new Error("Session terminated"));
      }
    }

    return Promise.reject(error);
  }
);

// Generic API request methods
export const api = {
  get: <T>(url: string, params?: unknown) =>
    apiClient.get<T>(url, { params }).then((res) => res.data),

  post: <T>(url: string, data?: unknown) =>
    apiClient.post<T>(url, data).then((res) => res.data),

  put: <T>(url: string, data?: unknown) =>
    apiClient.put<T>(url, data).then((res) => res.data),

  patch: <T>(url: string, data?: unknown) =>
    apiClient.patch<T>(url, data).then((res) => res.data),

  delete: <T>(url: string) =>
    apiClient.delete<T>(url).then((res) => res.data),

  getApiResponse: <T>(url: string, params?: unknown) =>
    apiClient.get<T>(url, { params, _isApiResponseMethod: true } as ExtendedAxiosRequestConfig).then((res) => res.data),

  postApiResponse: <T>(url: string, data?: unknown) =>
    apiClient.post<T>(url, data, { _isApiResponseMethod: true } as ExtendedAxiosRequestConfig).then((res) => res.data),
  putApiResponse: <T>(url: string, data?: unknown) =>
    apiClient.put<T>(url, data, { _isApiResponseMethod: true } as ExtendedAxiosRequestConfig).then((res) => res.data),

  patchApiResponse: <T>(url: string, data?: unknown) =>
    apiClient.patch<T>(url, data, { _isApiResponseMethod: true } as ExtendedAxiosRequestConfig).then((res) => res.data),

  deleteApiResponse: <T>(url: string) =>
    apiClient.delete<T>(url, { _isApiResponseMethod: true } as ExtendedAxiosRequestConfig).then((res) => res.data),

  // Delete with body için özel metod
  deleteWithBody: <T>(url: string, data?: unknown) =>
    apiClient.delete<T>(url, { data }).then((res) => res.data),

  // File upload için özel metod
  postFormData: <T>(url: string, formData: FormData) => {
    // FormData için Content-Type header'ını kaldır, axios otomatik ayarlayacak
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };
    return apiClient.post<T>(url, formData, config).then((res) => res.data);
  },
};

export default api;
