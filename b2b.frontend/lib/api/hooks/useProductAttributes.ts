import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import api from '../client';
import { ProductAttributeDto, ProductAttributeCreateDto, ProductAttributeUpdateDto, ProductAttributeFilterParams } from '@/app/[locale]/admin/product-attributes/types';

export const useProductAttributes = (filters: ProductAttributeFilterParams = {}) => {
  return useQuery({
    queryKey: ['product-attributes', filters],
    queryFn: () => api.get<ProductAttributeDto[]>('/productattribute', filters),
  });
};

// Get variant attributes for a specific category
export const useVariantAttributes = (categoryId?: string) => {
  return useQuery({
    queryKey: ['variant-attributes', categoryId],
    queryFn: () => api.get<ProductAttributeDto[]>('/productattribute', {
      categoryId,
      isVariantAttribute: true
    }),
    enabled: !!categoryId,
  });
};

// Get ALL attributes (variant + list) for a specific category
export const useAllAttributes = (categoryId?: string) => {
  return useQuery({
    queryKey: ['all-attributes', categoryId],
    queryFn: () => api.get<ProductAttributeDto[]>(`/productattribute/category/${categoryId}`),
    enabled: !!categoryId,
  });
};

export const useProductAttribute = (id: string) => {
  return useQuery({
    queryKey: ['product-attribute', id],
    queryFn: () => api.get<ProductAttributeDto>(`/productattribute/${id}`),
    enabled: !!id,
  });
};

export const useCreateProductAttribute = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (newAttr: ProductAttributeCreateDto) => api.post<ProductAttributeDto>('/productattribute', newAttr),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['product-attributes'] });
    },
  });
};

export const useUpdateProductAttribute = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (updateAttr: ProductAttributeUpdateDto) => api.put<ProductAttributeDto>(`/productattribute/${updateAttr.id}`, updateAttr),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['product-attributes'] });
    },
  });
};

export const useDeleteProductAttribute = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, force = false }: { id: string; force?: boolean }) =>
      api.delete(`/productattribute/${id}${force ? '?force=true' : ''}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['product-attributes'] });
    },
  });
};

// Server-side function to get all attributes
export const getProductAttributes = async (): Promise<ProductAttributeDto[]> => {
  return api.get<ProductAttributeDto[]>('/productattribute');
};
