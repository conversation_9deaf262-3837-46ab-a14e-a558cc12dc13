import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import api from '../client';
import { ProductAttributeValueDto, ProductAttributeValueCreateDto, ProductAttributeValueUpdateDto } from '@/app/[locale]/admin/product-attributes/types';

export const useProductAttributeValues = (attributeId?: string) => {
  return useQuery({
    queryKey: ['product-attribute-values', attributeId],
    queryFn: () => api.get<ProductAttributeValueDto[]>(`/productattributevalue?attributeId=${attributeId}`),
    enabled: !!attributeId,
  });
};

export const useProductAttributeValue = (id: string) => {
  return useQuery({
    queryKey: ['product-attribute-value', id],
    queryFn: () => api.get<ProductAttributeValueDto>(`/productattributevalue/${id}`),
    enabled: !!id,
  });
};

export const useCreateProductAttributeValue = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (newValue: ProductAttributeValueCreateDto) => api.post<ProductAttributeValueDto>('/productattributevalue', newValue),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['product-attribute-values', data.attributeId] });
    },
  });
};

export const useUpdateProductAttributeValue = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (updateValue: ProductAttributeValueUpdateDto) =>
      api.put<ProductAttributeValueDto>(`/productattributevalue/${updateValue.id}`, updateValue),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['product-attribute-values', data.attributeId] });
    },
  });
};

export const useDeleteProductAttributeValue = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: string) => api.deleteApiResponse(`/productattributevalue/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['product-attribute-values'] });
    },
  });
};
