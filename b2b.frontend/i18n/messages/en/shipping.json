{"shipping": {"title": "Shipping Settings", "single": "Shipping Carrier", "details": "Shipping Carrier Details", "list": "Shipping Carriers List", "add": "Add Shipping Carrier", "addDescription": "Create new shipping carrier", "edit": "Edit Shipping Carrier", "editDescription": "Edit existing shipping carrier", "delete": "Delete Shipping Carrier", "search": "Search shipping carriers...", "noCarriers": "No shipping carriers found", "availableCarriers": "Available Shipping Carriers", "implementedOnly": "Implemented Only", "activeOnly": "Active Only", "fields": {"staticId": "Static ID", "name": "Carrier Name", "shortCode": "Short Code", "description": "Description", "isImplemented": "Implemented", "isActive": "Active", "apiUrl": "API URL", "apiKey": "API Key", "logoUrl": "Logo URL", "sortOrder": "Sort Order", "settings": "Settings", "trackingUrl": "Tracking URL", "createdAt": "Created At", "updatedAt": "Updated At", "secretKey": "Secret Key", "username": "Username", "password": "Password", "testMode": "Test Mode", "notes": "Notes"}, "status": {"active": "Active", "inactive": "Inactive", "implemented": "Implemented", "notImplemented": "Not Implemented"}, "actions": {"activate": "Activate", "deactivate": "Deactivate", "addSetting": "Add Setting", "editSettings": "Edit Settings", "validateSettings": "Validate Settings", "testConnection": "Test Connection", "viewDetails": "View Details", "toggleStatus": "Toggle Status", "saveSettings": "Save Settings", "cancelEdit": "Cancel Edit", "deleteConfirm": "Are you sure you want to delete this shipping carrier?", "activateConfirm": "Are you sure you want to activate this shipping carrier?", "deactivateConfirm": "Are you sure you want to deactivate this shipping carrier?"}, "messages": {"settingsUpdateSuccess": "Shipping carrier settings updated successfully", "settingsUpdateError": "Error updating shipping carrier settings", "statusUpdateSuccess": "Shipping carrier status updated successfully", "statusUpdateError": "Error updating shipping carrier status", "validationSuccess": "Shipping carrier settings are valid", "validationError": "Shipping carrier settings are invalid", "connectionSuccess": "Shipping carrier connection successful", "connectionError": "Shipping carrier connection failed", "loadingCarriers": "Loading shipping carriers...", "noPermission": "You don't have permission for this action", "carrierNotFound": "Shipping carrier not found", "invalidSettings": "Invalid settings", "requiredField": "This field is required", "noSettings": "No settings found yet"}, "placeholders": {"name": "e.g. DHL Express", "shortCode": "e.g. DHL", "description": "Enter carrier description", "apiUrl": "https://api.example.com", "apiKey": "Enter your API key", "logoUrl": "/images/carriers/logo.png", "trackingUrl": "https://tracking.example.com/{trackingKey}", "searchCarriers": "Search by name, short code or description...", "secretKey": "Enter your secret key", "username": "Enter your username", "password": "Enter your password", "sortOrder": "0", "notes": "Additional notes...", "settingKey": "Setting key (e.g: ApiUrl)", "settingValue": "Setting value"}, "tabs": {"general": "General Information", "settings": "API Settings", "advanced": "Advanced Settings"}, "help": {"staticId": "Unique number automatically assigned by the system", "shortCode": "Unique short code for the carrier (uppercase)", "apiUrl": "API endpoint URL of the shipping carrier", "apiKey": "API key obtained from the shipping carrier", "trackingUrl": "URL for tracking queries (use {trackingKey} placeholder)", "sortOrder": "Order number to be used in listing", "settingsInfo": "Shipping carrier settings are stored as key-value pairs", "keyValueFormat": "Each setting contains a key and value", "commonKeys": "Common keys: ApiUrl, ApiKey, TestMode, WsUserName, WsPassword", "booleanValues": "Use 'true' or 'false' for boolean values"}, "settingsDialog": {"title": "{name} Settings", "description": "Edit shipping carrier API settings and general configuration.", "keyValueSettings": "Setting Parameters"}}}