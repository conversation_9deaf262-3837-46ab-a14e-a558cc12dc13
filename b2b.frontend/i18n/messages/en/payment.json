{"payment": {"title": "Payment Management", "list": "Payment list and management", "add": "Add New Payment", "edit": "Edit Payment", "detail": "Payment Detail", "delete": "Delete Payment", "search": "Search Payment", "filters": "Filters", "status": "Status", "amount": "Amount", "paymentMethod": "Payment Method", "orderNumber": "Order Number", "customer": "Customer", "transactionId": "Transaction ID", "date": "Date", "actions": "Actions", "description": "Description", "response": "Response", "responseCode": "Response Code", "responseMessage": "Response Message", "createdAt": "Created At", "updatedAt": "Updated At", "noPayments": "No payments yet", "noSearchResults": "No search results found", "loading": "Loading...", "error": "Error", "success": "Success", "pending": "Pending", "processing": "Processing", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled", "refunded": "Refunded", "allStatuses": "All Statuses", "searchPlaceholder": "Search by order number, customer name or transaction ID...", "statusFilter": "Status Filter", "totalPayments": "Total Payments", "paymentCount": "Payment Count", "analytics": "Analytics", "view": "View", "update": "Update", "updateStatus": "Update Status", "confirmDelete": "Are you sure you want to delete this payment?", "deleteSuccess": "Payment deleted successfully", "updateSuccess": "Payment updated successfully", "createSuccess": "Payment created successfully", "noPermission": "You don't have permission to view this page", "providers": {"title": "Payment Providers", "single": "Payment Provider", "details": "Payment Provider Details", "list": "Payment Providers List", "add": "Add Payment Provider", "addDescription": "Create new payment provider", "edit": "Edit Payment Provider", "editDescription": "Edit existing payment provider", "delete": "Delete Payment Provider", "search": "Search payment providers...", "noProviders": "No payment providers found", "availableProviders": "Available Payment Providers", "implementedOnly": "Implemented Only", "activeOnly": "Active Only", "fields": {"staticId": "Static ID", "name": "Provider Name", "shortCode": "Short Code", "description": "Description", "isImplemented": "Implemented", "isActive": "Active", "apiUrl": "API URL", "apiKey": "API Key", "secretKey": "Secret Key", "logoUrl": "Logo URL", "sortOrder": "Sort Order", "settings": "Settings", "createdAt": "Created At", "updatedAt": "Updated At"}, "status": {"active": "Active", "inactive": "Inactive", "implemented": "Implemented", "notImplemented": "Not Implemented"}, "actions": {"activate": "Activate", "deactivate": "Deactivate", "editSettings": "Edit Settings", "validateSettings": "Validate Settings", "testConnection": "Test Connection", "viewDetails": "View Details", "toggleStatus": "Toggle Status", "saveSettings": "Save Settings", "cancelEdit": "Cancel Edit", "deleteConfirm": "Are you sure you want to delete this payment provider?", "activateConfirm": "Are you sure you want to activate this payment provider?", "deactivateConfirm": "Are you sure you want to deactivate this payment provider?", "addSetting": "Add Setting"}, "messages": {"settingsUpdateSuccess": "Payment provider settings updated successfully", "settingsUpdateError": "Error updating payment provider settings", "statusUpdateSuccess": "Payment provider status updated successfully", "statusUpdateError": "Error updating payment provider status", "validationSuccess": "Payment provider settings are valid", "validationError": "Payment provider settings are invalid", "connectionSuccess": "Payment provider connection successful", "connectionError": "Payment provider connection failed", "loadingProviders": "Loading payment providers...", "noPermission": "You don't have permission for this action", "providerNotFound": "Payment provider not found", "invalidSettings": "Invalid settings", "requiredField": "This field is required", "noSettings": "No settings found yet"}, "placeholders": {"name": "e.g. <PERSON><PERSON>", "shortCode": "e.g. STRIPE", "description": "Enter provider description", "apiUrl": "https://api.example.com", "apiKey": "Enter your API key", "secretKey": "Enter your secret key", "logoUrl": "https://example.com/logo.png", "searchProviders": "Search by name, short code or description...", "merchantId": "Enter your merchant ID", "sortOrder": "0", "notes": "Additional notes...", "settingKey": "Setting key (e.g: apiUrl)", "settingValue": "Setting value"}, "tabs": {"general": "General Information", "settings": "API Settings", "advanced": "Advanced Settings"}, "help": {"staticId": "Unique number automatically assigned by the system", "shortCode": "Unique short code for the provider (uppercase)", "apiUrl": "API endpoint URL of the payment provider", "apiKey": "API key obtained from the payment provider", "secretKey": "Secret key obtained from the payment provider", "sortOrder": "Order number to be used in listing", "settingsInfo": "Payment provider settings are stored as key-value pairs", "keyValueFormat": "Each setting contains a key and value", "commonKeys": "Common keys: apiUrl, apiKey, secretKey, merchantId, testMode", "booleanValues": "Use 'true' or 'false' for boolean values"}, "settingsDialog": {"title": "{name} Settings", "description": "Edit payment provider API settings and general configuration.", "keyValueSettings": "Setting Parameters"}}}}