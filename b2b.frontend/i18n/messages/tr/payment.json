{"payment": {"title": "<PERSON><PERSON><PERSON>", "list": "Ö<PERSON>mel<PERSON>", "add": "<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "detail": "Ödeme <PERSON>", "delete": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "filters": "<PERSON><PERSON><PERSON><PERSON>", "status": "Durum", "amount": "<PERSON><PERSON>", "paymentMethod": "<PERSON><PERSON><PERSON>", "orderNumber": "Sipariş No", "customer": "Müş<PERSON>i", "transactionId": "İşlem ID", "date": "<PERSON><PERSON><PERSON>", "actions": "İşlemler", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "response": "Yan<PERSON>t", "responseCode": "Yanıt <PERSON>", "responseMessage": "<PERSON><PERSON><PERSON>", "createdAt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noPayments": "Henüz ödeme kaydı yok", "noSearchResults": "<PERSON><PERSON> son<PERSON>u bulu<PERSON>ı", "loading": "Yükleniyor...", "error": "<PERSON><PERSON>", "success": "Başarılı", "pending": "Beklemede", "processing": "İşleniyor", "completed": "Tamamlandı", "failed": "Başarısız", "cancelled": "İptal Edildi", "refunded": "<PERSON>ade Edildi", "allStatuses": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "<PERSON><PERSON><PERSON><PERSON>, müşteri adı veya işlem ID ile ara...", "statusFilter": "Durum Filtresi", "totalPayments": "Toplam Ödemeler", "paymentCount": "Ödeme <PERSON>", "analytics": "<PERSON><PERSON><PERSON>", "view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "updateStatus": "<PERSON><PERSON> Güncelle", "confirmDelete": "Bu ödemeyi silmek istediğinizden emin misiniz?", "deleteSuccess": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateSuccess": "<PERSON><PERSON>me başar<PERSON><PERSON>", "createSuccess": "Ödeme başarıyla oluşturuldu", "noPermission": "Bu sayfayı görüntüleme yetkiniz yok", "orderInfo": "Sipariş Bilgileri", "creditCard": "<PERSON><PERSON><PERSON>", "debitCard": "Banka Kartı", "bankTransfer": "Havale/EFT", "cash": "Nakit", "check": "Çek", "other": "<PERSON><PERSON><PERSON>", "descriptionPlaceholder": "Ödeme açıklaması", "descriptionRequired": "Açıklama zorunludur", "amountRequired": "<PERSON><PERSON>", "amountMinError": "Tutar 0'dan büyük olmalıdır", "updating": "Güncelleniyor...", "updateError": "<PERSON><PERSON>me g<PERSON> hata o<PERSON>", "providers": {"title": "Ödeme Sağlayıcıları", "single": "Ödeme Sağlayıcısı", "details": "Ödeme Sağlayıcısı Detayları", "list": "Ödeme Sağlayıcıları Listesi", "add": "Ödeme Sağlayıcısı Ekle", "addDescription": "Yeni ödeme sağlayıcısı oluştur", "edit": "Ödeme Sağlayıcısı Düzenle", "editDescription": "Mevcut ödeme sağlayıcısını düzenle", "delete": "Ödeme Sağlayıcısı Sil", "search": "Ödeme sağlayıcılarında ara...", "noProviders": "Ödeme sağlayıcısı bulunamadı", "availableProviders": "Kullanılabilir Ödeme Sağlayıcıları", "implementedOnly": "<PERSON><PERSON><PERSON>", "activeOnly": "Sadece Aktif <PERSON>", "fields": {"staticId": "Statik ID", "name": "Sağlayıcı Adı", "shortCode": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "isImplemented": "Entegre Edildi", "isActive": "Aktif", "apiUrl": "API URL", "apiKey": "API Anahtarı", "secretKey": "<PERSON><PERSON><PERSON>", "logoUrl": "Logo URL", "sortOrder": "Sıralama", "settings": "<PERSON><PERSON><PERSON>", "createdAt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantId": "Mağaza ID", "testMode": "Test Modu", "notes": "Notlar"}, "status": {"active": "Aktif", "inactive": "<PERSON><PERSON><PERSON>", "implemented": "Entegre", "notImplemented": "Enteg<PERSON>"}, "actions": {"activate": "Aktifleştir", "deactivate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "editSettings": "Ayarları Düzenle", "validateSettings": "Ayarları Doğrula", "testConnection": "Bağlantıyı Test Et", "viewDetails": "Detayları Görüntüle", "toggleStatus": "<PERSON><PERSON><PERSON>", "saveSettings": "Ayarları Kaydet", "cancelEdit": "Düzenlemeyi İptal Et", "deleteConfirm": "Bu ödeme sağlayıcısını silmek istediğinizden emin misiniz?", "activateConfirm": "Bu ödeme sağlayıcısını aktifleştirmek istediğinizden emin misiniz?", "deactivateConfirm": "Bu ödeme sağlayıcısını pasifleştirmek istediğinizden emin misiniz?", "addSetting": "<PERSON><PERSON>"}, "messages": {"settingsUpdateSuccess": "Ödeme sağlayıcısı ayarları başarıyla güncellendi", "settingsUpdateError": "Ödeme sağlayıcısı ayarları güncellenirken hata oluştu", "statusUpdateSuccess": "Ödeme <PERSON>ğlayıcısı durumu başarıyla güncellendi", "statusUpdateError": "Ödeme <PERSON>ğlayıcısı durumu güncellenirken hata oluştu", "validationSuccess": "Ödeme sağlayıcısı ayarları geçerli", "validationError": "Ödeme sağlayıcısı ayarları geçersiz", "connectionSuccess": "Ödeme sağlayıcısı bağlantısı başarılı", "connectionError": "Ödeme sağlayıcısı bağlantısı başarısız", "loadingProviders": "Ödeme sağlayıcıları yükleniyor...", "noPermission": "Bu iş<PERSON> için <PERSON> bulunmuyor", "providerNotFound": "Ödeme sağlayıcısı bulunamadı", "invalidSettings": "Geçersiz <PERSON>", "requiredField": "<PERSON><PERSON> <PERSON><PERSON>", "noSettings": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> bulu<PERSON>"}, "placeholders": {"name": "örn. Iyzico", "shortCode": "örn. IYZICO", "description": "Ödeme sağlayıcısı açıklaması girin", "apiUrl": "https://api.example.com", "apiKey": "API anahtarınızı girin", "secretKey": "Gizli anahtarınızı girin", "logoUrl": "https://example.com/logo.png", "searchProviders": "Sağlayıcı adı, kısa kod veya açıklama ile ara...", "merchantId": "Mağaza ID'nizi girin", "sortOrder": "0", "notes": "<PERSON>k notlar...", "settingKey": "<PERSON><PERSON> an<PERSON> (örn: apiUrl)", "settingValue": "<PERSON><PERSON>"}, "tabs": {"general": "<PERSON><PERSON>", "settings": "API Ayarları", "advanced": "Gelişmiş <PERSON>"}, "help": {"staticId": "Sistem tarafından otomatik atanan benzersiz numara", "shortCode": "Ödeme sağlayıcısı için benzersiz kısa kod (büyük harf)", "apiUrl": "Ödeme sağlayıcısının API endpoint URL'i", "apiKey": "Ödeme sağlayıcısından alınan API anahtarı", "secretKey": "Ödeme sağlayıcısından alınan gizli anahtar", "sortOrder": "Listeleme sırasında kullanılacak sıra numarası", "settingsInfo": "Ödeme sağlayıcısı ayarları anahtar-değer çiftleri şeklinde saklanır", "keyValueFormat": "Her ayar bir anahtar ve <PERSON> içerir", "commonKeys": "Yaygın an<PERSON>: apiUrl, apiKey, secretKey, merchantId, testMode", "booleanValues": "<PERSON><PERSON><PERSON> için 'true' veya 'false' kull<PERSON><PERSON>n"}, "settingsDialog": {"title": "{name} <PERSON><PERSON><PERSON><PERSON>", "description": "Ödeme sağlayıcısı API ayarlarını ve genel yapılandırmayı düzenleyin.", "keyValueSettings": "<PERSON><PERSON>"}}}}