{"product": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "single": "<PERSON><PERSON><PERSON><PERSON>", "details": "<PERSON><PERSON><PERSON><PERSON>", "list": "<PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "editProduct": "<PERSON><PERSON><PERSON><PERSON>", "editProductDescription": "Mevcut ürü<PERSON><PERSON> düzen<PERSON>in ve güncelleyin", "delete": "<PERSON><PERSON><PERSON><PERSON>", "noProducts": "<PERSON><PERSON><PERSON><PERSON> bulunamadı", "fields": {"name": "İsim", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON>", "discountedPrice": "İndirimli <PERSON>", "stock": "Stok", "category": "<PERSON><PERSON><PERSON>", "brand": "<PERSON><PERSON>", "sku": "Stok Kodu", "barcode": "Barkod", "images": "<PERSON><PERSON><PERSON><PERSON>", "status": "Durum", "variants": "<PERSON><PERSON><PERSON>", "productType": "<PERSON><PERSON><PERSON><PERSON>", "point": "<PERSON><PERSON>"}, "status": {"active": "Aktif", "inactive": "<PERSON><PERSON><PERSON>", "outOfStock": "Stokta Yok"}, "filters": {"title": "<PERSON><PERSON><PERSON><PERSON>", "price": "<PERSON>yat <PERSON>", "category": "<PERSON><PERSON><PERSON>", "brand": "<PERSON><PERSON>", "status": "Durum", "apply": "<PERSON><PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON><PERSON>", "search": "Ara...", "min": "Min", "max": "Max", "minStock": "<PERSON>", "maxStock": "<PERSON>"}, "sort": {"title": "Sıralama", "priceAsc": "Fiyat: Düşükten Yükseğe", "priceDesc": "Fiyat: Yüksekten Düşüğe", "nameAsc": "İsim: <PERSON><PERSON><PERSON><PERSON>", "nameDesc": "İsim: <PERSON><PERSON><PERSON> A'<PERSON>", "newest": "<PERSON>", "oldest": "<PERSON>"}, "productType": {"Simple": "<PERSON><PERSON><PERSON>", "Variant": "Varyantlı", "Grouped": "Gruplu"}, "viewMode": {"table": "<PERSON><PERSON><PERSON>", "card": "<PERSON><PERSON>"}, "validation": {"nameRequired": "Ürün adı zorunludur.", "productTypeRequired": "Ürün tipi zorunludur.", "skuRequired": "SKU zorunludur.", "categoryRequired": "<PERSON><PERSON>i se<PERSON>imi <PERSON>du<PERSON>.", "priceRequired": "Fiyat zorunludur ve 0'dan büyük olmalıdır.", "stockRequired": "Stok miktarı zorunludur ve 0'dan büyük olmalıdır.", "slugRequired": "Slug zorunludur."}, "faq": {"deleteFAQ": "Soruyu silmek üzeresiniz", "deleteFAQConfirmation": "Bu soruyu silmek istediğinizden emin misiniz?", "addFAQ": "<PERSON><PERSON>", "editFAQ": "<PERSON><PERSON><PERSON>", "updateFAQ": "<PERSON><PERSON><PERSON>", "question": "<PERSON><PERSON>", "answer": "<PERSON>va<PERSON>", "editProductFAQ": "Ürün SSS'ini Düzenle", "addProductFAQDescription": "Ürün SSS'ine yeni bir soru e<PERSON>in.", "addProductFAQ": "Ürün SSS'ine Soru Ekle", "editProductFAQDescription": "Ürün SSS'indeki soruyu düzen<PERSON>."}, "combobox": {"selectCategory": "<PERSON><PERSON><PERSON>", "selectBrand": "<PERSON><PERSON>", "searchCategories": "Kategorilerde ara...", "searchBrands": "Markalarda ara...", "noResults": "<PERSON><PERSON><PERSON> bulu<PERSON>adı", "itemsSelected": "<PERSON><PERSON><PERSON>"}, "form": {"productInfo": "<PERSON><PERSON><PERSON><PERSON>", "productInfoDesc": "Temel ürün bilgileri ve detayları", "productImages": "<PERSON><PERSON><PERSON><PERSON>", "basicInfo": "<PERSON><PERSON>", "productName": "<PERSON><PERSON><PERSON><PERSON>", "productNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> adını girin", "sku": "Stok Kodu", "skuPlaceholder": "Stok kodunu girin", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "descriptionPlaceholder": "<PERSON>rün açıklamasını girin", "categoryAndBrand": "<PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "selectCategory": "<PERSON><PERSON><PERSON>", "brand": "<PERSON><PERSON>", "selectBrand": "<PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON>", "stock": "Stok", "barcode": "Barkod", "barcodePlaceholder": "Barkod numarasını girin", "priceAndStock": "Fiyat ve Stok", "statusAndDates": "<PERSON>rum <PERSON>", "saveChanges": "Değişiklikleri Kaydet", "normalPrice": "Normal Fiyat (₺)", "discountPrice": "<PERSON><PERSON><PERSON><PERSON> (₺)", "createdAt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lastUpdate": "<PERSON>"}, "tabs": {"details": "Detaylar", "attributes": "<PERSON><PERSON><PERSON><PERSON>", "specifications": "<PERSON><PERSON><PERSON><PERSON>", "variants": "<PERSON><PERSON><PERSON><PERSON>", "seo": "SEO", "faq": "SSS", "reviews": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "attributes": {"title": "Özellik", "value": "<PERSON><PERSON><PERSON>", "actions": "İşlemler", "addattribute": "<PERSON><PERSON><PERSON>", "none": "Tanımlanmamış"}, "specifications": {"title": "Özellik Adı", "value": "Özellik Değeri", "actions": "İşlemler", "addSpecification": "<PERSON><PERSON><PERSON>"}, "variants": {"variantType": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "stock": "Stok", "actions": "İşlemler", "addVariant": "<PERSON><PERSON><PERSON>"}, "seo": {"title": "SEO Başlığı", "metaDescription": "Meta Açıklama", "keywords": "<PERSON><PERSON><PERSON>", "keywordsPlaceholder": "<PERSON><PERSON><PERSON> (virgülle ayırın)", "ogImage": "Open Graph Görseli", "changeOgImage": "OG Görseli Değiştir", "structuredData": "Yapılandırılmış Veri (LD+JSON)"}, "reviews": {"addReview": "Değerlendirme Ekle", "averageRating": "Ortalama Puan", "outOf5": "/ 5"}, "actions": {"actions": "İşlemler", "edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "backToProducts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updatePrice": "<PERSON><PERSON><PERSON>", "updateStock": "Stok Güncelle", "viewProductPage": "Ü<PERSON>ün <PERSON>ını Görüntüle", "salesReports": "Satış Raporları", "deleteProduct": "Ürünü <PERSON>"}, "placeholders": {"seoTitle": "SEO başlığı", "metaDescription": "<PERSON><PERSON>"}, "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "addImage": "<PERSON><PERSON><PERSON><PERSON>"}, "variant": {"addProductVariant": "<PERSON><PERSON><PERSON><PERSON>", "editProductVariant": "Ürün V<PERSON>antını Düzenle", "selectVariantTypeAndOption": "Eklemek istediğiniz varyant tipini ve seçeneğini seçin.", "editVariantDescription": "Varyant bilgilerini düzenleyin ve güncelleyin.", "variantType": "<PERSON><PERSON><PERSON>", "option": "Seçenek", "selectVariantType": "Vary<PERSON> tipi seçin", "selectOption": "Seçenek seçin", "skuPlaceholder": "Otomatik oluşturulacak", "stock": "Stok", "price": "<PERSON><PERSON><PERSON>", "salePrice": "İndirimli <PERSON>", "optional": "İsteğe bağlı", "active": "Aktif", "updateVariant": "Varyantı Güncelle"}}