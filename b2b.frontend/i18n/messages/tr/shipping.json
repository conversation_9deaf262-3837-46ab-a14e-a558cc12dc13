{"shipping": {"title": "<PERSON><PERSON>", "single": "Kargo Firması", "details": "Kargo Firması Detayları", "list": "Kargo Firmaları Listesi", "add": "Kargo Firması Ekle", "addDescription": "Yeni kargo firması oluştur", "edit": "Kargo Firması Düzenle", "editDescription": "Mevcut kargo firmasını düzenle", "delete": "Kargo Firması Sil", "search": "Kargo firmalarında ara...", "noCarriers": "Kargo firması bulunamadı", "availableCarriers": "Kullanılabilir Kargo Firmaları", "implementedOnly": "<PERSON><PERSON><PERSON>", "activeOnly": "Sadece Aktif <PERSON>", "fields": {"staticId": "Statik ID", "name": "Firma Adı", "shortCode": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "isImplemented": "Entegre Edildi", "isActive": "Aktif", "apiUrl": "API URL", "apiKey": "API Anahtarı", "logoUrl": "Logo URL", "sortOrder": "Sıralama", "settings": "<PERSON><PERSON><PERSON>", "trackingUrl": "Takip URL", "createdAt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "secretKey": "<PERSON><PERSON><PERSON>", "username": "Kullanıcı Adı", "password": "Şifre", "testMode": "Test Modu", "notes": "Notlar"}, "status": {"active": "Aktif", "inactive": "<PERSON><PERSON><PERSON>", "implemented": "Entegre", "notImplemented": "Enteg<PERSON>"}, "actions": {"activate": "Aktifleştir", "deactivate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "editSettings": "Ayarları Düzenle", "validateSettings": "Ayarları Doğrula", "testConnection": "Bağlantıyı Test Et", "viewDetails": "Detayları Görüntüle", "toggleStatus": "<PERSON><PERSON><PERSON>", "saveSettings": "Ayarları Kaydet", "cancelEdit": "Düzenlemeyi İptal Et", "deleteConfirm": "Bu kargo firmasını silmek istediğinizden emin misiniz?", "activateConfirm": "Bu kargo firmasını aktifleştirmek istediğinizden emin misiniz?", "deactivateConfirm": "Bu kargo firmasını pasifleştirmek istediğinizden emin misiniz?", "addSetting": "<PERSON><PERSON>"}, "messages": {"settingsUpdateSuccess": "Kargo firması ayarları başarıyla güncellendi", "settingsUpdateError": "Kargo firması ayarları güncellenirken hata oluştu", "statusUpdateSuccess": "Kargo firması durumu başarıyla güncellendi", "statusUpdateError": "Kargo firması durumu güncellenirken hata oluştu", "validationSuccess": "Kargo firması ayarları geçerli", "validationError": "Kargo firması ayarları geçersiz", "connectionSuccess": "Kargo firması bağlantısı başarılı", "connectionError": "Kargo firması bağlantısı başarısız", "loadingCarriers": "Kargo firmaları yükleniyor...", "noPermission": "Bu iş<PERSON> için <PERSON> bulunmuyor", "carrierNotFound": "Kargo firması bulunamadı", "invalidSettings": "Geçersiz <PERSON>", "requiredField": "<PERSON><PERSON> <PERSON><PERSON>", "noSettings": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> bulu<PERSON>"}, "placeholders": {"name": "örn. <PERSON><PERSON><PERSON><PERSON><PERSON>", "shortCode": "örn. YURTICI", "description": "Kargo firması açıklaması girin", "apiUrl": "https://api.example.com", "apiKey": "API anahtarınızı girin", "logoUrl": "/images/carriers/logo.png", "trackingUrl": "https://tracking.example.com/{trackingKey}", "searchCarriers": "<PERSON>rma adı, kısa kod veya açıklama ile ara...", "secretKey": "Gizli anahtarınızı girin", "username": "Kullanıcı adınızı girin", "password": "Şifrenizi girin", "sortOrder": "0", "notes": "<PERSON>k notlar...", "settingKey": "<PERSON><PERSON> (örn: ApiUrl)", "settingValue": "<PERSON><PERSON>"}, "tabs": {"general": "<PERSON><PERSON>", "settings": "API Ayarları", "advanced": "Gelişmiş <PERSON>"}, "help": {"staticId": "Sistem tarafından otomatik atanan benzersiz numara", "shortCode": "Kargo firması için ben<PERSON> kısa kod (büyük harf)", "apiUrl": "Kargo firmasının API endpoint URL'i", "apiKey": "Kargo firmasından alınan API anahtarı", "trackingUrl": "Takip sorgulaması için URL ({trackingKey} placeholder k<PERSON><PERSON><PERSON><PERSON>)", "sortOrder": "Listeleme sırasında kullanılacak sıra numarası", "settingsInfo": "Kargo firması ayarları anahtar-değer çiftleri şeklinde saklanır", "keyValueFormat": "Her ayar bir anahtar ve <PERSON> içerir", "commonKeys": "Yaygın <PERSON>: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TestMode, WsUserName, WsPassword", "booleanValues": "<PERSON><PERSON><PERSON> için 'true' veya 'false' kull<PERSON><PERSON>n"}, "settingsDialog": {"title": "{name} <PERSON><PERSON><PERSON><PERSON>", "description": "Kargo firması API ayarlarını ve genel yapılandırmayı düzenleyin.", "keyValueSettings": "<PERSON><PERSON>"}}}