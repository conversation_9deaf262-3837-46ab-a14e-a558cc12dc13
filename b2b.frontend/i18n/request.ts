import { getRequestConfig } from 'next-intl/server';
import { hasLocale } from 'next-intl';
import { routing } from './routing';

export default getRequestConfig(async ({ requestLocale }) => {
  // Typically corresponds to the `[locale]` segment
  const requested = await requestLocale;
  const locale = hasLocale(routing.locales, requested)
    ? requested
    : routing.defaultLocale;

  return {
    locale,
    messages: {
      ...(await import(`./messages/${locale}/common.json`)).default,
      ...(await import(`./messages/${locale}/order.json`)).default,
      ...(await import(`./messages/${locale}/product.json`)).default,
      ...(await import(`./messages/${locale}/category.json`)).default,
      ...(await import(`./messages/${locale}/brand.json`)).default,
      ...(await import(`./messages/${locale}/auth.json`)).default,
      ...(await import(`./messages/${locale}/productAttribute.json`)).default,
      ...(await import(`./messages/${locale}/role.json`)).default,
      ...(await import(`./messages/${locale}/payment.json`)).default,
      ...(await import(`./messages/${locale}/user.json`)).default,
      ...(await import(`./messages/${locale}/shipment.json`)).default,
      ...(await import(`./messages/${locale}/customer.json`)).default,
      ...(await import(`./messages/${locale}/coupon.json`)).default,
      ...(await import(`./messages/${locale}/mail.json`)).default,
      ...(await import(`./messages/${locale}/campaign.json`)).default,
      ...(await import(`./messages/${locale}/productReview.json`)).default,
      ...(await import(`./messages/${locale}/system-settings.json`)).default,
      ...(await import(`./messages/${locale}/shipping.json`)).default,
      ...(await import(`./messages/${locale}/payment.json`)).default


    },
  };
});